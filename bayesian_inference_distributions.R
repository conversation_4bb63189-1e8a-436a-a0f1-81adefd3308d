# ===============================================================================
# Bayesian and Frequentist Inference: Normal vs Cauchy Distributions
# Comparing inference methods under light-tailed vs heavy-tailed outcomes
# ===============================================================================

# Load required libraries
library(ggplot2)
library(brms)
library(loo)

# ===============================================================================
# VISUAL COMPARISON: Normal, Student-t, and Cauchy Distributions
# ===============================================================================

# Generate comparison plot of the three distributions
x <- seq(-5, 5, length.out = 1000)
df <- data.frame(
  x = rep(x, 3),
  density = c(dnorm(x, 0, 1),
              dt(x, df = 3),
              dcauchy(x, 0, 1)),
  Distribution = factor(rep(c("Normal(0,1)", "Student-t(3)", "Cauchy(0,1)"), each = length(x)))
)

comparison_plot <- ggplot(df, aes(x = x, y = density, color = Distribution)) +
  geom_line(size = 1.2) +
  theme_minimal(base_size = 14) +
  labs(title = "Comparison of Normal, Student-t, and Cauchy Distributions",
       x = "Value", y = "Density") +
  scale_color_manual(values = c("blue", "darkgreen", "red"))

print(comparison_plot)

# ===============================================================================
# PART 1: SIMULATING NORMAL OUTCOMES
# ===============================================================================

# Set seed for reproducibility
set.seed(1)
n <- 20

# Generate data using normal random variables
treat <- c(rep(0, n/2), rep(1, n/2))
y <- treat * rnorm(n, mean = 1) + (1 - treat) * rnorm(n, mean = 0)

cat("=== PART 1: NORMAL OUTCOMES ===\n")
cat("Treatment group mean:", mean(y[treat == 1]), "\n")
cat("Control group mean:", mean(y[treat == 0]), "\n")

# Step 1: Difference-in-Means
diff_in_means <- mean(y[treat == 1]) - mean(y[treat == 0])
cat("Difference-in-means:", diff_in_means, "\n")

# Compare to difference-in-medians
diff_in_medians <- median(y[treat == 1]) - median(y[treat == 0])
cat("Difference-in-medians:", diff_in_medians, "\n")

# Step 2: Classical Regression / t-Statistic
lm_norm <- lm(y ~ treat)
print(summary(lm_norm))

# How many s.e.'s is the coefficient from true value 1?
se_distance <- (lm_norm$coefficients[2] - 1) / sqrt(vcov(lm_norm)[2, 2])
cat("Distance from true value (1) in standard errors:", se_distance, "\n")

# Step 3: Permutation Tests
perm_dist <- sapply(1:1000, function(x) t.test(y ~ sample(treat))$estimate)
perm_pvalue <- mean(abs(perm_dist) > abs(diff_in_means))
cat("Permutation test p-value:", perm_pvalue, "\n")

# Step 4: Bootstrap
bootstrap_distribution <- sapply(1:1000, function(x) {
  boot_samp <- c(
    sample(which(treat == 1), sum(treat == 1), TRUE),
    sample(which(treat == 0), sum(treat == 0), TRUE)
  )
  diff(t.test(y[boot_samp] ~ treat[boot_samp])$estimate)
})

cat("Bootstrap distribution summary:\n")
print(summary(bootstrap_distribution))
plot(density(bootstrap_distribution), main = "Bootstrap Distribution - Normal Outcomes")

# Step 5: Bayesian Posterior
b1 <- brm(y ~ treat, data = data.frame(y, treat))
print(summary(b1))

# ===============================================================================
# PART 2: SIMULATING CAUCHY OUTCOMES
# ===============================================================================

set.seed(1)
n <- 20

# Generate data using Cauchy random variables
treat <- c(rep(0, n/2), rep(1, n/2))
y_c <- treat * (1 + rcauchy(n)) + (1 - treat) * rcauchy(n)

cat("\n=== PART 2: CAUCHY OUTCOMES ===\n")
cat("Treatment group mean:", mean(y_c[treat == 1]), "\n")
cat("Control group mean:", mean(y_c[treat == 0]), "\n")
cat("Treatment group median:", median(y_c[treat == 1]), "\n")
cat("Control group median:", median(y_c[treat == 0]), "\n")

# Step 1: Difference-in-Means
diff_in_means_c <- mean(y_c[treat == 1]) - mean(y_c[treat == 0])
cat("Difference-in-means:", diff_in_means_c, "\n")

# Compare to difference-in-medians
diff_in_medians_c <- median(y_c[treat == 1]) - median(y_c[treat == 0])
cat("Difference-in-medians:", diff_in_medians_c, "\n")

# Step 2: Linear Regression
lm_cauchy <- lm(y_c ~ treat)
print(summary(lm_cauchy))

# Distance from true value 1
se_distance_c <- (lm_cauchy$coefficients[2] - 1) / sqrt(vcov(lm_cauchy)[2, 2])
cat("Distance from true value (1) in standard errors:", se_distance_c, "\n")

# Step 3: Permutation Test
perm_dist_c <- sapply(1:1000, function(x) t.test(y_c ~ sample(treat))$estimate)
perm_pvalue_c <- mean(abs(perm_dist_c) > abs(diff_in_means_c))
cat("Permutation test p-value:", perm_pvalue_c, "\n")

# Step 4: Bootstrap
bootstrap_distribution_c <- sapply(1:1000, function(x) {
  boot_samp <- c(
    sample(which(treat == 1), sum(treat == 1), TRUE),
    sample(which(treat == 0), sum(treat == 0), TRUE)
  )
  diff(t.test(y_c[boot_samp] ~ treat[boot_samp])$estimate)
})

cat("Bootstrap distribution summary (Cauchy):\n")
print(summary(bootstrap_distribution_c))
plot(density(bootstrap_distribution_c), main = "Bootstrap Distribution - Cauchy Outcomes")

# Step 5: Bayesian Posterior (Normal Likelihood)
b1_c <- brm(y_c ~ treat, data = data.frame(y_c, treat))
print(summary(b1_c))

# Step 6: Bayesian Posterior with Fixed Cauchy (ν = 1)
b2_c <- brm(
  y_c ~ treat,
  family = student(),
  data = data.frame(y_c, treat),
  prior = prior(constant(1), class = "nu")  # Fix ν = 1 → Cauchy
)
print(summary(b2_c))

# Step 7: Bayesian Posterior with Estimated ν (Student-t Likelihood)
b3_c <- brm(
  y_c ~ treat,
  family = student(),
  data = data.frame(y_c, treat)
)
print(summary(b3_c))

# Step 8: Comparing models using LOO
loo_b1  <- loo(b1_c, moment_match = TRUE)
loo_b2c <- loo(b2_c, moment_match = TRUE)
loo_b3c <- loo(b3_c, moment_match = TRUE)

cat("\n=== MODEL COMPARISON ===\n")
print(loo_compare(loo_b1, loo_b2c, loo_b3c))

# ===============================================================================
# COMPARISON SUMMARIES
# ===============================================================================

cat("\n=== SUMMARY COMPARISON ===\n")
cat("Normal Outcomes:\n")
cat("  Diff-in-means:", diff_in_means, "\n")
cat("  Diff-in-medians:", diff_in_medians, "\n")
cat("  Regression coefficient:", lm_norm$coefficients[2], "\n")
cat("  Permutation p-value:", perm_pvalue, "\n")

cat("\nCauchy Outcomes:\n")
cat("  Diff-in-means:", diff_in_means_c, "\n")
cat("  Diff-in-medians:", diff_in_medians_c, "\n")
cat("  Regression coefficient:", lm_cauchy$coefficients[2], "\n")
cat("  Permutation p-value:", perm_pvalue_c, "\n")

# ===============================================================================
# TAIL PROBABILITY DEMONSTRATION
# ===============================================================================

cat("\n=== TAIL PROBABILITY COMPARISON ===\n")
values <- 1:5
normal_probs <- dnorm(values, 0, 1)
t_probs <- dt(values, df = 3)
cauchy_probs <- dcauchy(values, 0, 1)

tail_comparison <- data.frame(
  Value = values,
  Normal = round(normal_probs, 6),
  Student_t = round(t_probs, 6),
  Cauchy = round(cauchy_probs, 6)
)

print(tail_comparison)

cat("\n=== ANALYSIS COMPLETE ===\n")
cat("This script demonstrates the behavior of different inference methods\n")
cat("under light-tailed (Normal) vs heavy-tailed (Cauchy) distributions.\n") 