library(pacman)
p_load(tidyverse, brms, blme, lme4, mvtnorm, Matrix)

# Load the data
Data1 <- read.csv("https://raw.githubusercontent.com/mannheimSDS/mannheimSDS.github.io/refs/heads/main/data/EfronMorris.csv",head=TRUE)
dim(Data1)
head(Data1)

# Calculate the RMSE of the mean
rmse.mean <- mean((Data1$avg45 - Data1$avgSeason)^2)^.5
rmse.mean

# Calculate the RMSE of the linear model
rmse.lm <- mean((Data1$avg45 - lm(avg45 ~ avgSeason, data = Data1)$fitted)^2)^.5
rmse.lm

# Calculate the shrinkage prior
y <- Data1$avg45
s2 <- mean(y)*(1 - mean(y))/45
1-(s2/17)/(var(y)/15)

# Calculate the shrinkage prior using the optimize function
js.func<-function(c.js){
	js.curr<-mean(Data1$avg45)+c.js*(Data1$avg45-mean(Data1$avg45))
	mean((js.curr-Data1$avgSeason)^2)^.5
}

opt1<-optimize(js.func,interval=c(0,1))
opt1

# Plot the shrinkage prior
x.plot<-seq(-.1,1.1,by=0.01); plot(x.plot,sapply(x.plot,js.func),type="l",xlab="Shrinkage Factor",ylab="RMSE"); points(0.212,js.func(0.212))
points(opt1$min,opt1$obj);
text(0.325,0.0525,"James-Stein");
text(0.1,0.05,"Optimal"); arrows(x=0.325,y0=0.0515,x1=0.212,y1=js.func(0.212),length=.1); arrows(x=0.1,y0=0.0495,x1=opt1$min,y1=opt1$obj,length=.1)



# Define the objective function
objective <- function(par, y, Z){
  # par = mean, log var, log re var
  vcov_obs <- as.matrix(exp(par[2]) *
                          ((Z %*% t(Z)) * exp(par[3])
                           + Diagonal(n=nrow(Z))))
  out <- mvtnorm::dmvnorm(x = y,
    mean = rep(par[1], nrow(Z)),
    sigma = vcov_obs,  log = TRUE)
  return(out)
}

# Define the function to estimate the random effects
est.all <- function(y, res) {
  l1 <- lmer(y ~ (1 | res), REML = FALSE)
  wide_res <- sparseMatrix(i = 1:length(res), j = match(res, sort(unique(res))), x = 1)
  l1_extract <- c(fixef(l1)[1], summary(l1)$sigma, sqrt(summary(l1)$varcor$res[1,1]))
  good_init <- l1_extract
  good_init[3] <- good_init[3]/good_init[2]
  good_init[2:3] <- 2 * log(good_init[2:3])
  man1 <- optim(par = good_init, fn = objective, method = 'BFGS', control = list(fnscale = -1, reltol = 0, abstol = 0),  y = y, Z = wide_res)
  man1$par[-1] <- sqrt(exp(man1$par[-1]))
  lmer_test <- c(man1$par[1], man1$par[2], man1$par[2] * man1$par[3])
  EM_b <- solve(t(wide_res) %*% wide_res + 1/man1$par[3]^2 * Diagonal(n = ncol(wide_res))) %*%
    t(wide_res) %*% (y-man1$par[1])
  return(list(
    "optimREs" = as.vector(EM_b),
    "lmerREs" = lme4::ranef(l1)$res[, 1]
  ))
}

# Simulate data
n<-200
set.seed(3)
res.map <- rnorm(20)
names(res.map) <- letters[1:20]

# Sample the random effects
res.obs <- sample(letters[1:20], n, T)
res.true <- res.map[res.obs]

# Simulate the data
y_bigREs <- res.true + rnorm(n, sd = 2)

# Estimate the rand om effects
bigREs <- est.all(y_bigREs, res.obs)

# Plot the results
par(mfrow = c(1, 2))
range_ests <- range(c(unlist(bigREs), res.map))
with(bigREs, {
  plot(
    lmerREs,
    optimREs,
    asp = 1,
    xlab = "Estimates from LMER",
    ylab = "Estimates from optim",
    bty  = "n",
    main = "Linear, Large REs",
    xlim = range_ests, ylim = range_ests, col = "red"
  )
  points(lmerREs, optimREs, col = "blue", pch = 3)
  abline(a=0,b=1)
})

abline(0, 1, lty = 2)

plot(bigREs$optimREs-bigREs$lmerREs, main="Difference between REs", ylab="Difference",xlab="RE")







