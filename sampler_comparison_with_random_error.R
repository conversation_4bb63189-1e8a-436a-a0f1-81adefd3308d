library(pacman)
p_load(mixtools, MASS, MCMCpack)

# Synthetic data
set.seed(1)
n <- 10
p <- 2
M <- diag(2)
M[M == 0] <- 2^-.5        # ρ ≈ 0.707 off‑diagonal
X <- MASS::mvrnorm(n, c(0, 0), Sigma = M)
X <- apply(X, 2, scale)
y <- X %*% c(2, 3) + rnorm(n)
y <- y - mean(y)

# MAP estimates (for comparison)
theta.map <- lm(y ~ X)$coef[-1]
sigma2.map <- sum(lm(y ~ X)$residuals^2) / (n - p - 1)  # ML estimate of sigma^2

# Helper functions for unknown variance 
# Now params = c(beta1, beta2, log(sigma^2)) 
logpost <- function(params) {
  beta <- params[1:2]
  log_sigma2 <- params[3]
  sigma2 <- exp(log_sigma2)
  
  # Log-likelihood
  resid_ss <- sum((y - X %*% beta)^2)
  log_lik <- -n/2 * log_sigma2 - resid_ss / (2 * sigma2)
  
  # Jeffreys prior for sigma^2
  log_prior <- -log_sigma2
  
  # Return negative log-posterior (for minimization)
  return(-(log_lik + log_prior))
}

gradpost <- function(params) {
  beta <- params[1:2]
  log_sigma2 <- params[3]
  sigma2 <- exp(log_sigma2)
  
  resid <- y - X %*% beta
  resid_ss <- sum(resid^2)
  
  # Gradient w.r.t. beta
  grad_beta <- as.vector(t(X) %*% resid) / sigma2
  
  # Gradient w.r.t. log(sigma^2)
  grad_log_sigma2 <- -n/2 + resid_ss / (2 * sigma2)
  
  # Return negative gradient (for minimization)
  return(-c(grad_beta, grad_log_sigma2))
}

source('https://raw.githubusercontent.com/mannheimSDS/mannheimSDS.github.io/refs/heads/main/data/HMCFunctions_ICPSR.R')

# Test the functions
test_params <- c(0, 0, 0)  # beta1=0, beta2=0, log(sigma^2)=0 (sigma^2=1)
cat("Log-posterior at test point:", -logpost(test_params), "\n")


# 3D parameter space

# Hamiltonian Monte Carlo for 3D parameter space
HMC <- function (U, grad_U, epsilon, L, current_q) {
  q <- current_q
  p <- rnorm(length(q))
  current_p <- p
  p <- p - epsilon * grad_U(q) / 2
  for (i in 1:L) {
    q <- q + epsilon * p
    if (i != L) p <- p - epsilon * grad_U(q)
  }
  p <- p - epsilon * grad_U(q) / 2
  p <- -p
  current_U <- U(current_q);    current_K <- sum(current_p^2) / 2
  proposed_U <- U(q);           proposed_K <- sum(p^2) / 2
  if (runif(1) < exp(current_U - proposed_U + current_K - proposed_K))
    return(q) else return(current_q)
}

# Metropolis Hastings for 3D parameter space
MH <- function (U, epsilon, current_q) {
  q <- current_q + epsilon * rnorm(length(current_q))
  if (runif(1) < exp(U(current_q) - U(q)))
    q else current_q
}

####### Part 2 ########

# Initialization and pre-allocation for samplers
# 3 parameters: beta1, beta2, log(sigma^2)
params.init <- params.curr <- c(0, 0, 0)
params.all <- NULL
params.all$Gibbs1 <- matrix(NA, nr = 3, nc = 1000)
params.all$Gibbs2 <- matrix(NA, nr = 3, nc = 1000)
params.all$MH     <- matrix(NA, nr = 3, nc = 1200)
params.all$HMC    <- matrix(NA, nr = 3, nc = 200)

# Gibbs sampler: analytical posteriors
# beta | sigma^2, y ~ N((X'X)^{-1}X'y, sigma^2 * (X'X)^{-1})
# sigma^2 | beta, y ~ InverseGamma((n-p)/2, ||y - X*beta||^2/2)

# Joint Gibbs sampler (using analytical forms)
set.seed(123)
params.curr <- params.init
XTX_inv <- solve(crossprod(X))
XTy <- crossprod(X, y)
beta_hat <- XTX_inv %*% XTy

system.time({
  for (i in 1:1000) {
    # Sample beta | sigma^2
    sigma2 <- exp(params.curr[3])
    beta_new <- MASS::mvrnorm(1, beta_hat, sigma2 * XTX_inv)
    
    # Sample sigma^2 | beta
    resid_ss <- sum((y - X %*% beta_new)^2)
    sigma2_new <- MCMCpack::rinvgamma(1, shape = (n-p)/2, scale = resid_ss/2)
    
    params.curr <- c(beta_new, log(sigma2_new))
    params.all$Gibbs2[, i] <- params.curr
  }
})

# Coordinate-wise Gibbs sampler
params.curr <- params.init
system.time({
  for (i in 1:1000) {
    set.seed(i)
    params.all$Gibbs1[, i] <- params.curr
    
    sigma2 <- exp(params.curr[3])
    
    # Update beta1 | beta2, sigma^2
    resid1 <- y - X[, 2] * params.curr[2]
    var1 <- sigma2 / sum(X[, 1]^2)
    mean1 <- sum(X[, 1] * resid1) / sum(X[, 1]^2)
    params.curr[1] <- rnorm(1, mean1, sqrt(var1))
    
    # Update beta2 | beta1, sigma^2
    resid2 <- y - X[, 1] * params.curr[1]
    var2 <- sigma2 / sum(X[, 2]^2)
    mean2 <- sum(X[, 2] * resid2) / sum(X[, 2]^2)
    params.curr[2] <- rnorm(1, mean2, sqrt(var2))
    
    # Update sigma^2 | beta1, beta2
    resid_ss <- sum((y - X %*% params.curr[1:2])^2)
    sigma2_new <- MCMCpack::rinvgamma(1, shape = (n-p)/2, scale = resid_ss/2)
    params.curr[3] <- log(sigma2_new)
  }
})

# HMC sampler
params.curr <- params.init
system.time({
  for (i in 1:200) {
    set.seed(i)
    params.all$HMC[, i] <- params.curr
    params.curr <- HMC(logpost, gradpost, .1, 10, params.curr)
  }
})

# Metropolis-Hastings sampler
params.curr <- params.init
proposal_sd <- c(0.2, 0.2, 0.3)  # Different step sizes for different parameters
system.time({
  for (i in 1:1200) {
    set.seed(i)
    params.all$MH[, i] <- params.curr
    params.curr <- MH(logpost, 0.15, params.curr)
  }
})

# Convert log(sigma^2) back to sigma^2 for plotting
sigma2.all <- list()
for(method in names(params.all)) {
  sigma2.all[[method]] <- exp(params.all[[method]][3, ])
}

####### Part 2.5: Plotting Parameter Trajectories ########

# Plot beta parameters
par(mfrow = c(2, 2))

# Joint Gibbs - beta space
plot(
  params.all$Gibbs2[1, ],
  params.all$Gibbs2[2, ],
  type = "l",
  xlim = c(-1, 3.5),
  ylim = c(-.1, 4.5),
  main = "Joint Gibbs Sampler",
  xlab = "beta1", ylab = "beta2"
)
points(theta.map[1], theta.map[2], pch = 19, col = "red", cex = 2)

# Coordinate-wise Gibbs - beta space
plot(
  params.all$Gibbs1[1, ],
  params.all$Gibbs1[2, ],
  type = "l",
  xlim = c(-1, 3.5),
  ylim = c(-.1, 4.5),
  main = "Coordinate-wise Gibbs",
  xlab = "beta1", ylab = "beta2"
)
points(theta.map[1], theta.map[2], pch = 19, col = "red", cex = 2)

# Metropolis-Hastings - beta space
plot(
  params.all$MH[1, ],
  params.all$MH[2, ],
  type = "l",
  xlim = c(-1, 3.5),
  ylim = c(-.1, 4.5),
  main = "Metropolis-Hastings",
  xlab = "beta1", ylab = "beta2"
)
points(theta.map[1], theta.map[2], pch = 19, col = "red", cex = 2)

# HMC - beta space
plot(
  params.all$HMC[1, ],
  params.all$HMC[2, ],
  type = "l",
  xlim = c(-1, 3.5),
  ylim = c(-.1, 4.5),
  main = "Hamiltonian Monte Carlo",
  xlab = "beta1", ylab = "beta2"
)
points(theta.map[1], theta.map[2], pch = 19, col = "red", cex = 2)

# Plot sigma^2 trajectories
par(mfrow = c(2, 2))

plot(sigma2.all$Gibbs2, type = "l", main = "Joint Gibbs - sigma^2", 
     ylab = "sigma^2", xlab = "Iteration")
abline(h = sigma2.map, col = "red", lwd = 2)

plot(sigma2.all$Gibbs1, type = "l", main = "Coordinate-wise Gibbs - sigma^2", 
     ylab = "sigma^2", xlab = "Iteration")
abline(h = sigma2.map, col = "red", lwd = 2)

plot(sigma2.all$MH, type = "l", main = "Metropolis-Hastings - sigma^2", 
     ylab = "sigma^2", xlab = "Iteration")
abline(h = sigma2.map, col = "red", lwd = 2)

plot(sigma2.all$HMC, type = "l", main = "HMC - sigma^2", 
     ylab = "sigma^2", xlab = "Iteration")
abline(h = sigma2.map, col = "red", lwd = 2)

####### Part 3 ########
# Trace plots and autocorrelation plots
ac <- function(x) { x <- x[!is.na(x)]; cor(x[-1], x[-length(x)]) }

par(mfrow = c(2, 2))

# Trace plots for beta1
plot(params.all$Gibbs2[1, !is.na(params.all$Gibbs2[1, ])],
     type = "l", xlab = "Iteration", ylab = "beta1",
     main = paste("Joint Gibbs, AC =", round(ac(params.all$Gibbs2[1, ]), 3)))

plot(params.all$Gibbs1[1, !is.na(params.all$Gibbs1[1, ])],
     type = "l", xlab = "Iteration", ylab = "beta1",
     main = paste("Coord Gibbs, AC =", round(ac(params.all$Gibbs1[1, ]), 3)))

plot(params.all$MH[1, !is.na(params.all$MH[1, ])],
     type = "l", xlab = "Iteration", ylab = "beta1",
     main = paste("MH, AC =", round(ac(params.all$MH[1, ]), 3)))

plot(params.all$HMC[1, !is.na(params.all$HMC[1, ])],
     type = "l", xlab = "Iteration", ylab = "beta1",
     main = paste("HMC, AC =", round(ac(params.all$HMC[1, ]), 3)))

# Print autocorrelations for all parameters
cat("\nAutocorrelations:\n")
for(method in names(params.all)) {
  cat(method, ":\n")
  cat("  beta1:", round(ac(params.all[[method]][1, ]), 3), "\n")
  cat("  beta2:", round(ac(params.all[[method]][2, ]), 3), "\n")
  cat("  sigma^2:", round(ac(sigma2.all[[method]]), 3), "\n")
}

####### Part 4 ########
# Multiple‑chain Convergence Check (HMC)
params.all$HMC2 <- params.all$HMC3 <- params.all$HMC * NA

# Chain 2: start from different initial values
params.curr <- c(5, 5, log(5))  # Different initial sigma^2 too
for (i in 1:200) {
  set.seed(i)
  params.all$HMC2[, i] <- params.curr
  params.curr <- HMC(logpost, gradpost, .1, 10, params.curr)
}

# Chain 3: start from different initial values
params.curr <- c(5, -5, log(0.1))
for (i in 1:200) {
  set.seed(i)
  params.all$HMC3[, i] <- params.curr
  params.curr <- HMC(logpost, gradpost, .1, 10, params.curr)
}

# Convergence plots
par(mfrow = c(2, 2))

# beta1 convergence
plot(1:200, params.all$HMC[1, !is.na(params.all$HMC[1, ])],
     type = "l", xlab = "Iteration", ylab = "beta1",
     ylim = range(c(params.all$HMC[1, ], params.all$HMC2[1, ], params.all$HMC3[1, ]), na.rm = TRUE),
     main = "HMC Convergence - beta1")
lines(1:200, params.all$HMC2[1, !is.na(params.all$HMC2[1, ])], col = "red")
lines(1:200, params.all$HMC3[1, !is.na(params.all$HMC3[1, ])], col = "blue")
legend("topright", c("Chain 1", "Chain 2", "Chain 3"), col = c("black", "red", "blue"), lty = 1)

# beta2 convergence
plot(1:200, params.all$HMC[2, !is.na(params.all$HMC[2, ])],
     type = "l", xlab = "Iteration", ylab = "beta2",
     ylim = range(c(params.all$HMC[2, ], params.all$HMC2[2, ], params.all$HMC3[2, ]), na.rm = TRUE),
     main = "HMC Convergence - beta2")
lines(1:200, params.all$HMC2[2, !is.na(params.all$HMC2[2, ])], col = "red")
lines(1:200, params.all$HMC3[2, !is.na(params.all$HMC3[2, ])], col = "blue")

# sigma^2 convergence
sigma2_HMC1 <- exp(params.all$HMC[3, ])
sigma2_HMC2 <- exp(params.all$HMC2[3, ])
sigma2_HMC3 <- exp(params.all$HMC3[3, ])

plot(1:200, sigma2_HMC1[!is.na(sigma2_HMC1)],
     type = "l", xlab = "Iteration", ylab = "sigma^2",
     ylim = range(c(sigma2_HMC1, sigma2_HMC2, sigma2_HMC3), na.rm = TRUE),
     main = "HMC Convergence - sigma^2")
lines(1:200, sigma2_HMC2[!is.na(sigma2_HMC2)], col = "red")
lines(1:200, sigma2_HMC3[!is.na(sigma2_HMC3)], col = "blue")

# Summary statistics
cat("\n\nPosterior Summary Statistics:\n")
cat("=================================\n")
for(method in names(params.all)) {
  if(all(is.na(params.all[[method]]))) next
  
  cat("\n", method, ":\n")
  cat("beta1: mean =", round(mean(params.all[[method]][1, ], na.rm = TRUE), 3),
      ", sd =", round(sd(params.all[[method]][1, ], na.rm = TRUE), 3), "\n")
  cat("beta2: mean =", round(mean(params.all[[method]][2, ], na.rm = TRUE), 3),
      ", sd =", round(sd(params.all[[method]][2, ], na.rm = TRUE), 3), "\n")
  cat("sigma^2: mean =", round(mean(sigma2.all[[method]], na.rm = TRUE), 3),
      ", sd =", round(sd(sigma2.all[[method]], na.rm = TRUE), 3), "\n")
}

cat("\nTrue values: beta = (", theta.map[1], ",", theta.map[2], "), sigma^2 =", sigma2.map, "\n")


