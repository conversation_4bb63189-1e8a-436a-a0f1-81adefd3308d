data_ert <- read.csv("https://raw.githubusercontent.com/vdeminstitute/ERT/master/inst/ert.csv")

summary(data_ert)

poly <- na.omit(data_ert$v2x_polyarchy)
summary(poly)

# Plot the density of poly
plot(density(poly))

# Simulate data from mixture of beta distributions
set.seed(123) 

# Function to generate new samples from the fitted mixture
generate_mixture_sample <- function(n, weight1 = 0.8, alpha1 = 2, beta1 = 8, 
                                   alpha2 = 5, beta2 = 2) {
  n1 <- rbinom(1, n, weight1)
  n2 <- n - n1
  component1 <- rbeta(n1, alpha1, beta1)
  component2 <- rbeta(n2, alpha2, beta2)
  return(c(component1, component2))
}

# Generate a new sample
simulated_data <- generate_mixture_sample(1000)

# Plot real data
plot(density(poly), main = "Real Data (Polyarchy)", 
     xlab = "Polyarchy Score", ylab = "Density", col = "blue", lwd = 2)

# Plot simulated data
plot(density(simulated_data), main = "Simulated Data (Beta Mixture)", 
     xlab = "Value", ylab = "Density", col = "red", lwd = 2)

# Plot both overlaid
plot(density(poly), main = "Comparison: Real vs Simulated", 
     xlab = "Value", ylab = "Density", col = "blue", lwd = 2)
lines(density(simulated_data), col = "red", lwd = 2, lty = 2)
legend("topright", legend = c("Real Data", "Simulated Data"), 
       col = c("blue", "red"), lwd = 2, lty = c(1, 2))

# Histogram comparison
hist(poly, breaks = 30, probability = TRUE, main = "Histograms Comparison",
     xlab = "Value", ylab = "Density", col = rgb(0,0,1,0.3), border = "blue")
hist(simulated_data, breaks = 30, probability = TRUE, add = TRUE,
     col = rgb(1,0,0,0.3), border = "red")
legend("topright", legend = c("Real Data", "Simulated Data"), 
       fill = c(rgb(0,0,1,0.3), rgb(1,0,0,0.3)))

# Reset to single plot
par(mfrow = c(1, 1))

# Print summary statistics for comparison
cat("Real Data Summary:\n")
print(summary(poly))
cat("\nSimulated Data Summary:\n")
print(summary(simulated_data))


############################################################################


# Compare CDFs 
# Create empirical CDFs
ecdf_real <- ecdf(poly)
ecdf_simulated <- ecdf(simulated_data)


# Plot CDFs on the same range 
x_range <- seq(0, 1, length.out = 1000)
cdf_real_values <- ecdf_real(x_range)
cdf_simulated_values <- ecdf_simulated(x_range)

plot(x_range, cdf_real_values, type = "l", 
     main = "CDF Comparison",
     xlab = "Value", ylab = "Cumulative Probability", 
     col = "blue", lwd = 2)
lines(x_range, cdf_simulated_values, col = "red", lwd = 2, lty = 2)
legend("bottomright", legend = c("Real Data CDF", "Simulated Data CDF"), 
       col = c("blue", "red"), lwd = 2, lty = c(1, 2))

# Calculate and display CDF differences
cdf_diff <- abs(cdf_real_values - cdf_simulated_values)
max_diff <- max(cdf_diff)
cat("\nCDF Comparison Metrics:\n")
cat("Maximum absolute difference between CDFs:", round(max_diff, 4), "\n")
cat("Mean absolute difference between CDFs:", round(mean(cdf_diff), 4), "\n")

# Plot the absolute difference between CDFs
plot(x_range, cdf_diff, type = "l", 
     main = "Absolute Difference Between CDFs",
     xlab = "Value", ylab = "Absolute CDF Difference", 
     col = "purple", lwd = 2)
abline(h = max_diff, col = "red", lty = 2)
text(0.5, max_diff + 0.01, paste("Max diff =", round(max_diff, 4)), 
     col = "red", adj = c(0.5, 0))










