a <- rbeta(100000000, 91, 11)
b <- rbeta(100000000, 3, 3)

mean(a > b) # 0.0001

# Manual two-proportion z-test
x1 <- 90; n1 <- 100
x2 <-  2; n2 <-   2

p1     <- x1 / n1
p2     <- x2 / n2
p_pool <- (x1 + x2) / (n1 + n2)
se     <- sqrt(p_pool * (1 - p_pool) * (1/n1 + 1/n2))

z       <- (p1 - p2) / se
p_value <- 2 * pnorm(-abs(z))
ci_low  <- (p1 - p2) - qnorm(0.975) * se
ci_high <- (p1 - p2) + qnorm(0.975) * se

z; p_value; c(ci_low, ci_high)
