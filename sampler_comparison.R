library(pacman)
p_load(mixtools, MASS)

# Synthetic data stays the same as in the previous code
set.seed(1)
n <- 10
p <- 2
M <- diag(2)
M[M == 0] <- 2^-.5        # ρ ≈ 0.707 off‑diagonal
X <- MASS::mvrnorm(n, c(0, 0), Sigma = M)
X <- apply(X, 2, scale)
y <- X %*% c(2, 3) + rnorm(n)
y <- y - mean(y)

theta.map <- lm(y ~ X)$coef[-1]
theta.map

# Helper functions
logpost <- function(theta) {
  sum((y - X %*% theta)^2) / 2
}

gradpost <- function(theta) {
  -as.vector(t(X) %*% (y - X %*% theta))
}

source('https://raw.githubusercontent.com/mannheimSDS/mannheimSDS.github.io/refs/heads/main/data/HMCFunctions_ICPSR.R')
## HMC(q) and MH(q) wrappers live in this file

logpost(c(0, 0))                                  # log‑density at origin
logpost(HMC(logpost, gradpost, .01, 10, c(0, 0))) # after 10 leapfrog steps

# Hamiltonian Monte Carlo
HMC <- function (U, grad_U, epsilon, L, current_q) {
  q <- current_q
  p <- rnorm(length(q))
  current_p <- p
  p <- p - epsilon * grad_U(q) / 2
  for (i in 1:L) {
    q <- q + epsilon * p
    if (i != L) p <- p - epsilon * grad_U(q)
  }
  p <- p - epsilon * grad_U(q) / 2
  p <- -p
  current_U <- U(current_q);    current_K <- sum(current_p^2) / 2
  proposed_U <- U(q);           proposed_K <- sum(p^2) / 2
  if (runif(1) < exp(current_U - proposed_U + current_K - proposed_K))
    return(q) else return(current_q)
}

# Metropolis Hastings
MH <- function (U, epsilon, current_q) {
  q <- current_q + epsilon * rnorm(length(current_q))
  if (runif(1) < exp(U(current_q) - U(q)))
    q else current_q
}



####### Part 2 ########

# Initialization and pre-allocation for samplers
theta.init <- theta.curr <- c(0, 0)
theta.all <- NULL
theta.all$Gibbs1 <- matrix(NA, nr = 2, nc = 2000)
theta.all$Gibbs2 <- matrix(NA, nr = 2, nc = 2000)
theta.all$MH     <- matrix(NA, nr = 2, nc = 2000)
theta.all$HMC    <- matrix(NA, nr = 2, nc = 2000)

# Gibbs sampler: draw from posterior (multivariate normal)
theta.all$Gibbs2 <- t(MASS::mvrnorm(1000, mu = theta.map, Sigma = solve(crossprod(X))))

# Gibbs sampler for theta.all$Gibbs1
theta.curr <- theta.init
system.time({
  for (i in 1:1000) {
    set.seed(i)
    theta.all$Gibbs1[, i] <- theta.curr
    theta.curr[1] <- rnorm(1,
      mean = lm(y - X[, 2] * theta.curr[2] ~ X[, 1])$coef[2],
      sd = 1 / sum(X[, 1]^2)^.5
    )
    theta.curr[2] <- rnorm(1,
      mean = lm(y - X[, 1] * theta.curr[1] ~ X[, 2])$coef[2],
      sd = 1 / sum(X[, 2]^2)^.5
    )
  }
})

# HMC sampler for theta.all$HMC
theta.curr <- theta.init
system.time({
  for (i in 1:200) {
    set.seed(i)
    theta.all$HMC[, i] <- theta.curr
    theta.curr <- HMC(logpost, gradpost, .45, 10, theta.curr)
  }
})


# Metropolis-Hastings sampler for theta.all$MH
theta.curr <- theta.init
system.time({
  for (i in 1:1200) {
    set.seed(i)
    theta.all$MH[, i] <- theta.curr
    theta.curr <- MH(logpost, .25, theta.curr)
  }
})

# Plotting the results for joint gibbs sampler
plot(
  theta.all$Gibbs2[1, ],
  theta.all$Gibbs2[2, ],
  type = "l",
  xlim = c(-1, 3.5),
  ylim = c(-.1, 4.5)
)
points(
  theta.map[1],
  theta.map[2],
  pch = 19,
  col = "red",
  cex = 2
)

for (i.alpha in 1:6)
  mixtools::ellipse(theta.map,
                    solve(crossprod(X)),
                    alpha = 10 ^ (-i.alpha),
                    col = gray(.5))


# Plotting the results for Cordinate-wise Gibbs sampler
plot(
  theta.all$Gibbs1[1, ],
  theta.all$Gibbs1[2, ],
  type = "l",
  xlim = c(-1, 3.5),
  ylim = c(-.1, 4.5)
)
points(
  theta.map[1],
  theta.map[2],
  pch = 19,
  col = "red",
  cex = 2
)
for (i.alpha in 1:6)
  mixtools::ellipse(
    theta.map,
    solve(crossprod(X)),
    alpha = 10^(-i.alpha),
    col = gray(.5)
  )


# Plotting the results for Metropolis-Hastings sampler
plot(
  theta.all$MH[1, ],
  theta.all$MH[2, ],
  type = "l",
  xlim = c(-1, 3.5),
  ylim = c(-.1, 4.5)
)

points(
  theta.map[1],
  theta.map[2],
  pch = 19,
  col = "red",
  cex = 2
)

for (i.alpha in 1:6)
  mixtools::ellipse(theta.map,
    solve(crossprod(X)),
    alpha = 10 ^ (-i.alpha),
    col = gray(.5))

# Plotting the results for Hamiltonian Monte Carlo sampler
plot(
  theta.all$HMC[1, ],
  theta.all$HMC[2, ],
  type = "l",
  xlim = c(-1, 3.5),
  ylim = c(-.1, 4.5)
)

points(
  theta.map[1],
  theta.map[2],
  pch = 19,
  col = "red",
  cex = 2
)

for (i.alpha in 1:6)
  mixtools::ellipse(theta.map,
    solve(crossprod(X)),
    alpha = 10 ^ (-i.alpha),
    col = gray(.5))

# No U-turn sampler
nuts1 <- HMC_NUTS(logpost, gradpost, .01, 1000, c(0, 0))
plot(nuts1[1,], type="l", xlab="L", ylab="beta1")


####### Part 3 ########
# Trace plots and autocorrelation plots
ac <- function(x) { x <- x[!is.na(x)]; cor(x[-1], x[-length(x)]) }

plot(theta.all$Gibbs2[1, !is.na(theta.all$Gibbs2[1, ])],
     type = "l",
     xlab = "Iteration",
     ylab = "beta1")

ac(theta.all$Gibbs2[1, ])

# Trace plot for Metropolis-Hastings sampler
plot(theta.all$MH[1, !is.na(theta.all$MH[1, ])],
     type = "l",
     xlab = "Iteration",
     ylab = "beta1")

ac(theta.all$MH[1, ])

# Trace plot for Hamiltonian Monte Carlo sampler
plot(theta.all$HMC[1, !is.na(theta.all$HMC[1, ])],
     type = "l",
     xlab = "Iteration",
     ylab = "beta1")

ac(theta.all$HMC[1, ])

# Trace plot for Cordinate-wise Gibbs sampler
plot(theta.all$Gibbs1[1, !is.na(theta.all$Gibbs1[1, ])],
     type = "l",
     xlab = "Iteration",
     ylab = "beta1")

ac(theta.all$Gibbs1[1, ])


####### Part 4 ########
# Multiple‑chain Convergence Check (HMC)
theta.all$HMC2 <- theta.all$HMC3 <- theta.all$HMC * NA
theta.curr <- c(5, 5)
for (i in 1:200) {
  set.seed(i)
  theta.all$HMC2[, i] <- theta.curr
  theta.curr <- HMC(logpost, gradpost, .1, 10, theta.curr)
}

theta.curr <- c(5, -5)
for (i in 1:200) {
  set.seed(i)
  theta.all$HMC3[, i] <- theta.curr
  theta.curr <- HMC(logpost, gradpost, .1, 10, theta.curr)
}

# Trace plot for Hamiltonian Monte Carlo sampler with different initial values
plot(
  1:200,
  theta.all$HMC[1, !is.na(theta.all$HMC[1, ])],
  type = "l",
  xlab = "Iteration",
  ylab = "beta1",
  ylim = c(-5, 9)
)
lines(1:200, theta.all$HMC2[1, !is.na(theta.all$HMC2[1, ])], col = "red")
lines(1:200, theta.all$HMC3[1, !is.na(theta.all$HMC3[1, ])], col = "blue")










