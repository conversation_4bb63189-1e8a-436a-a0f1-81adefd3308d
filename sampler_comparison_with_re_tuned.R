library(pacman)
p_load(mixtools, MASS)

# Simple inverse gamma sampler
rinvgamma <- function(n, shape, scale) {
  return(1 / rgamma(n, shape = shape, rate = scale))
}

# Synthetic data generation
set.seed(1)
n <- 10
p <- 2
M <- diag(2)
M[M == 0] <- 2^-.5        # ρ ≈ 0.707 off‑diagonal
X <- MASS::mvrnorm(n, c(0, 0), Sigma = M)
X <- apply(X, 2, scale)
y <- X %*% c(2, 3) + rnorm(n)
y <- y - mean(y)

# MAP estimates (for comparison)
theta.map <- lm(y ~ X)$coef[-1]
sigma2.map <- sum(lm(y ~ X)$residuals^2) / (n - p - 1)  # ML estimate of sigma^2
cat("MAP estimates: beta =", theta.map, ", sigma^2 =", sigma2.map, "\n")

####### Helper Functions for Unknown Variance Case #######
# Parameters: c(beta1, beta2, log(sigma^2)) for numerical stability

logpost <- function(params) {
  beta <- params[1:2]
  log_sigma2 <- params[3]
  sigma2 <- exp(log_sigma2)
  
  # Log-likelihood
  resid_ss <- sum((y - X %*% beta)^2)
  log_lik <- -n/2 * log_sigma2 - resid_ss / (2 * sigma2)
  
  # Jeffreys prior for sigma^2: p(sigma^2) ∝ 1/sigma^2
  log_prior <- -log_sigma2
  
  # Return negative log-posterior (for minimization)
  return(-(log_lik + log_prior))
}

gradpost <- function(params) {
  beta <- params[1:2]
  log_sigma2 <- params[3]
  sigma2 <- exp(log_sigma2)
  
  resid <- y - X %*% beta
  resid_ss <- sum(resid^2)
  
  # Gradient w.r.t. beta
  grad_beta <- as.vector(t(X) %*% resid) / sigma2
  
  # Gradient w.r.t. log(sigma^2)
  grad_log_sigma2 <- -n/2 + resid_ss / (2 * sigma2)
  
  # Return negative gradient (for minimization)
  return(-c(grad_beta, grad_log_sigma2))
}

# Load external HMC functions
source('https://raw.githubusercontent.com/mannheimSDS/mannheimSDS.github.io/refs/heads/main/data/HMCFunctions_ICPSR.R')

# Test the functions
test_params <- c(0, 0, 0)  # beta1=0, beta2=0, log(sigma^2)=0 (sigma^2=1)
cat("Log-posterior at test point:", -logpost(test_params), "\n")

####### Sampling Algorithm Definitions #######

# Standard HMC
HMC <- function (U, grad_U, epsilon, L, current_q) {
  q <- current_q
  p <- rnorm(length(q))
  current_p <- p
  p <- p - epsilon * grad_U(q) / 2
  for (i in 1:L) {
    q <- q + epsilon * p
    if (i != L) p <- p - epsilon * grad_U(q)
  }
  p <- p - epsilon * grad_U(q) / 2
  p <- -p
  current_U <- U(current_q);    current_K <- sum(current_p^2) / 2
  proposed_U <- U(q);           proposed_K <- sum(p^2) / 2
  if (runif(1) < exp(current_U - proposed_U + current_K - proposed_K))
    return(q) else return(current_q)
}

# Improved MH with parameter-specific proposals and acceptance monitoring
MH_improved <- function(U, proposal_sds, current_q) {
  proposed_q <- current_q + proposal_sds * rnorm(length(current_q))
  log_ratio <- U(current_q) - U(proposed_q)
  accept <- runif(1) < exp(log_ratio)
  if (accept) {
    return(list(q = proposed_q, accepted = TRUE))
  } else {
    return(list(q = current_q, accepted = FALSE))
  }
}

####### Parameter Tuning #######

cat("\n==== PARAMETER TUNING ====\n")

# HMC Parameter Tuning: Test different step sizes
cat("Tuning HMC parameters...\n")
hmc_test_epsilons <- seq(0.01, 0.5, by = 0.01)  # 50 values from 0.01 to 0.5
hmc_test_L_values <- c(3, 5, 8, 10, 12, 15, 18, 20, 25, 30, 35, 40, 45, 50)  # 14 values
hmc_acceptance_rates <- matrix(NA, length(hmc_test_epsilons), length(hmc_test_L_values))

for(i in seq_along(hmc_test_epsilons)) {
  for(j in seq_along(hmc_test_L_values)) {
    eps <- hmc_test_epsilons[i]
    L <- hmc_test_L_values[j]
    
    # 500 iterations
    accepts <- 0
    params_test <- c(0, 0, 0)  # Starting values
    for(k in 1:500) {
      set.seed(k)
      params_new <- HMC(logpost, gradpost, eps, L, params_test)
      if(!all(params_new == params_test)) accepts <- accepts + 1
      params_test <- params_new
    }
    hmc_acceptance_rates[i,j] <- accepts / 500
  }
}

# Find optimal HMC parameters (targeting ~65% acceptance)
target_acceptance <- 0.65
diff_matrix <- abs(hmc_acceptance_rates - target_acceptance)
min_idx <- which(diff_matrix == min(diff_matrix, na.rm = TRUE), arr.ind = TRUE)[1,]
optimal_epsilon <- hmc_test_epsilons[min_idx[1]]
optimal_L <- hmc_test_L_values[min_idx[2]]

cat("HMC Tuning Results:\n")
cat("  Optimal epsilon:", optimal_epsilon, "L:", optimal_L, 
    "Acceptance rate:", round(hmc_acceptance_rates[min_idx[1], min_idx[2]], 3), "\n")

# MH Parameter Tuning: Test different proposal variances
cat("Tuning MH parameters...\n")
mh_test_scales <- seq(0.01, 1.0, by = 0.02)  # 500 values from 0.01 to 1.0
mh_acceptance_rates <- rep(NA, length(mh_test_scales))

for(i in seq_along(mh_test_scales)) {
  scale <- mh_test_scales[i]
  proposal_sds <- c(scale, scale, scale * 1.5)  # Larger step for log(sigma^2)
  
  # 500 iterations
  accepts <- 0
  params_test <- c(0, 0, 0)
  for(k in 1:500) {
    set.seed(k)
    result <- MH_improved(logpost, proposal_sds, params_test)
    if(result$accepted) accepts <- accepts + 1
    params_test <- result$q
  }
  mh_acceptance_rates[i] <- accepts / 500
}

# Find optimal MH parameters (targeting ~25% acceptance)
target_mh_acceptance <- 0.25
optimal_mh_idx <- which.min(abs(mh_acceptance_rates - target_mh_acceptance))
optimal_mh_scale <- mh_test_scales[optimal_mh_idx]
optimal_proposal_sds <- c(optimal_mh_scale, optimal_mh_scale, optimal_mh_scale * 1.5)

cat("MH Tuning Results:\n")
cat("  Optimal scale:", optimal_mh_scale, "Acceptance rate:", 
    round(mh_acceptance_rates[optimal_mh_idx], 3), "\n")

####### Sampling #######

cat("\n==== RUNNING SAMPLERS ====\n")

# Initialization and pre-allocation for samplers
params.init <- params.curr <- c(0, 0, 0)  # beta1, beta2, log(sigma^2)
params.all <- NULL
params.all$Gibbs1 <- matrix(NA, nr = 3, nc = 1000)
params.all$Gibbs2 <- matrix(NA, nr = 3, nc = 1000)
params.all$MH     <- matrix(NA, nr = 3, nc = 1200)
params.all$HMC    <- matrix(NA, nr = 3, nc = 200)

# Joint Gibbs sampler (using analytical forms)
cat("Running Joint Gibbs sampler...\n")
set.seed(123)
params.curr <- params.init
XTX_inv <- solve(crossprod(X))
XTy <- crossprod(X, y)
beta_hat <- XTX_inv %*% XTy

system.time({
  for (i in 1:1000) {
    # Sample beta | sigma^2
    sigma2 <- exp(params.curr[3])
    beta_new <- MASS::mvrnorm(1, beta_hat, sigma2 * XTX_inv)
    
    # Sample sigma^2 | beta
    resid_ss <- sum((y - X %*% beta_new)^2)
    sigma2_new <- rinvgamma(1, shape = (n-p)/2, scale = resid_ss/2)
    
    params.curr <- c(beta_new, log(sigma2_new))
    params.all$Gibbs2[, i] <- params.curr
  }
})

# Coordinate-wise Gibbs sampler
cat("Running Coordinate-wise Gibbs sampler...\n")
params.curr <- params.init
system.time({
  for (i in 1:1000) {
    set.seed(i)
    params.all$Gibbs1[, i] <- params.curr
    
    sigma2 <- exp(params.curr[3])
    
    # Update beta1 | beta2, sigma^2
    resid1 <- y - X[, 2] * params.curr[2]
    var1 <- sigma2 / sum(X[, 1]^2)
    mean1 <- sum(X[, 1] * resid1) / sum(X[, 1]^2)
    params.curr[1] <- rnorm(1, mean1, sqrt(var1))
    
    # Update beta2 | beta1, sigma^2
    resid2 <- y - X[, 1] * params.curr[1]
    var2 <- sigma2 / sum(X[, 2]^2)
    mean2 <- sum(X[, 2] * resid2) / sum(X[, 2]^2)
    params.curr[2] <- rnorm(1, mean2, sqrt(var2))
    
    # Update sigma^2 | beta1, beta2
    resid_ss <- sum((y - X %*% params.curr[1:2])^2)
    sigma2_new <- rinvgamma(1, shape = (n-p)/2, scale = resid_ss/2)
    params.curr[3] <- log(sigma2_new)
  }
})

# Tuned HMC sampler
cat("Running tuned HMC sampler...\n")
params.curr <- params.init
hmc_accepts <- 0
system.time({
  for (i in 1:200) {
    set.seed(i)
    params.all$HMC[, i] <- params.curr
    params_old <- params.curr
    params.curr <- HMC(logpost, gradpost, optimal_epsilon, optimal_L, params.curr)
    if(!all(params.curr == params_old)) hmc_accepts <- hmc_accepts + 1
  }
})
cat("Final HMC acceptance rate:", round(hmc_accepts / 200, 3), "\n")

# Tuned Metropolis-Hastings sampler
cat("Running tuned MH sampler...\n")
params.curr <- params.init
mh_accepts <- 0
system.time({
  for (i in 1:1200) {
    set.seed(i)
    params.all$MH[, i] <- params.curr
    result <- MH_improved(logpost, optimal_proposal_sds, params.curr)
    if(result$accepted) mh_accepts <- mh_accepts + 1
    params.curr <- result$q
  }
})
cat("Final MH acceptance rate:", round(mh_accepts / 1200, 3), "\n")

# Convert log(sigma^2) back to sigma^2 for plotting
sigma2.all <- list()
for(method in names(params.all)) {
  sigma2.all[[method]] <- exp(params.all[[method]][3, ])
}

####### Plotting and Diagnostics #######

cat("\n==== GENERATING PLOTS ====\n")

# Plot beta parameter trajectories
par(mfrow = c(2, 2))

# Joint Gibbs - beta space
plot(
  params.all$Gibbs2[1, ],
  params.all$Gibbs2[2, ],
  type = "l",
  xlim = c(-1, 3.5),
  ylim = c(-.1, 4.5),
  main = "Joint Gibbs Sampler",
  xlab = "beta1", ylab = "beta2"
)
points(theta.map[1], theta.map[2], pch = 19, col = "red", cex = 2)

# Coordinate-wise Gibbs - beta space
plot(
  params.all$Gibbs1[1, ],
  params.all$Gibbs1[2, ],
  type = "l",
  xlim = c(-1, 3.5),
  ylim = c(-.1, 4.5),
  main = "Coordinate-wise Gibbs",
  xlab = "beta1", ylab = "beta2"
)
points(theta.map[1], theta.map[2], pch = 19, col = "red", cex = 2)

# Metropolis-Hastings - beta space
plot(
  params.all$MH[1, ],
  params.all$MH[2, ],
  type = "l",
  xlim = c(-1, 3.5),
  ylim = c(-.1, 4.5),
  main = "Tuned Metropolis-Hastings",
  xlab = "beta1", ylab = "beta2"
)
points(theta.map[1], theta.map[2], pch = 19, col = "red", cex = 2)

# HMC - beta space
plot(
  params.all$HMC[1, ],
  params.all$HMC[2, ],
  type = "l",
  xlim = c(-1, 3.5),
  ylim = c(-.1, 4.5),
  main = "Tuned Hamiltonian Monte Carlo",
  xlab = "beta1", ylab = "beta2"
)
points(theta.map[1], theta.map[2], pch = 19, col = "red", cex = 2)

# Plot sigma^2 trajectories
par(mfrow = c(2, 2))

plot(sigma2.all$Gibbs2, type = "l", main = "Joint Gibbs - sigma^2", 
     ylab = "sigma^2", xlab = "Iteration")
abline(h = sigma2.map, col = "red", lwd = 2)

plot(sigma2.all$Gibbs1, type = "l", main = "Coordinate-wise Gibbs - sigma^2", 
     ylab = "sigma^2", xlab = "Iteration")
abline(h = sigma2.map, col = "red", lwd = 2)

plot(sigma2.all$MH, type = "l", main = "Tuned MH - sigma^2", 
     ylab = "sigma^2", xlab = "Iteration")
abline(h = sigma2.map, col = "red", lwd = 2)

plot(sigma2.all$HMC, type = "l", main = "Tuned HMC - sigma^2", 
     ylab = "sigma^2", xlab = "Iteration")
abline(h = sigma2.map, col = "red", lwd = 2)

# Trace plots and autocorrelation
ac <- function(x) { x <- x[!is.na(x)]; cor(x[-1], x[-length(x)]) }

par(mfrow = c(2, 2))

# Trace plots for beta1
plot(params.all$Gibbs2[1, !is.na(params.all$Gibbs2[1, ])],
     type = "l", xlab = "Iteration", ylab = "beta1",
     main = paste("Joint Gibbs, AC =", round(ac(params.all$Gibbs2[1, ]), 3)))

plot(params.all$Gibbs1[1, !is.na(params.all$Gibbs1[1, ])],
     type = "l", xlab = "Iteration", ylab = "beta1",
     main = paste("Coord Gibbs, AC =", round(ac(params.all$Gibbs1[1, ]), 3)))

plot(params.all$MH[1, !is.na(params.all$MH[1, ])],
     type = "l", xlab = "Iteration", ylab = "beta1",
     main = paste("Tuned MH, AC =", round(ac(params.all$MH[1, ]), 3)))

plot(params.all$HMC[1, !is.na(params.all$HMC[1, ])],
     type = "l", xlab = "Iteration", ylab = "beta1",
     main = paste("Tuned HMC, AC =", round(ac(params.all$HMC[1, ]), 3)))

####### Convergence Analysis #######

cat("\n==== CONVERGENCE ANALYSIS ====\n")

# Multiple‑chain Convergence Check (HMC)
params.all$HMC2 <- params.all$HMC3 <- params.all$HMC * NA

# Chain 2: start from different initial values
params.curr <- c(5, 5, log(5))  # Different initial sigma^2 too
for (i in 1:200) {
  set.seed(i)
  params.all$HMC2[, i] <- params.curr
  params.curr <- HMC(logpost, gradpost, optimal_epsilon, optimal_L, params.curr)
}

# Chain 3: start from different initial values
params.curr <- c(5, -5, log(0.1))
for (i in 1:200) {
  set.seed(i)
  params.all$HMC3[, i] <- params.curr
  params.curr <- HMC(logpost, gradpost, optimal_epsilon, optimal_L, params.curr)
}

# Convergence plots
par(mfrow = c(2, 2))

# beta1 convergence
plot(1:200, params.all$HMC[1, !is.na(params.all$HMC[1, ])],
     type = "l", xlab = "Iteration", ylab = "beta1",
     ylim = range(c(params.all$HMC[1, ], params.all$HMC2[1, ], params.all$HMC3[1, ]), na.rm = TRUE),
     main = "HMC Convergence - beta1")
lines(1:200, params.all$HMC2[1, !is.na(params.all$HMC2[1, ])], col = "red")
lines(1:200, params.all$HMC3[1, !is.na(params.all$HMC3[1, ])], col = "blue")
legend("topright", c("Chain 1", "Chain 2", "Chain 3"), col = c("black", "red", "blue"), lty = 1)

# beta2 convergence
plot(1:200, params.all$HMC[2, !is.na(params.all$HMC[2, ])],
     type = "l", xlab = "Iteration", ylab = "beta2",
     ylim = range(c(params.all$HMC[2, ], params.all$HMC2[2, ], params.all$HMC3[2, ]), na.rm = TRUE),
     main = "HMC Convergence - beta2")
lines(1:200, params.all$HMC2[2, !is.na(params.all$HMC2[2, ])], col = "red")
lines(1:200, params.all$HMC3[2, !is.na(params.all$HMC3[2, ])], col = "blue")

# sigma^2 convergence
sigma2_HMC1 <- exp(params.all$HMC[3, ])
sigma2_HMC2 <- exp(params.all$HMC2[3, ])
sigma2_HMC3 <- exp(params.all$HMC3[3, ])

plot(1:200, sigma2_HMC1[!is.na(sigma2_HMC1)],
     type = "l", xlab = "Iteration", ylab = "sigma^2",
     ylim = range(c(sigma2_HMC1, sigma2_HMC2, sigma2_HMC3), na.rm = TRUE),
     main = "HMC Convergence - sigma^2")
lines(1:200, sigma2_HMC2[!is.na(sigma2_HMC2)], col = "red")
lines(1:200, sigma2_HMC3[!is.na(sigma2_HMC3)], col = "blue")

####### Summary Statistics #######

cat("\n==== SUMMARY STATISTICS ====\n")

# Print autocorrelations for all parameters
cat("\nAutocorrelations:\n")
for(method in names(params.all)) {
  cat(method, ":\n")
  cat("  beta1:", round(ac(params.all[[method]][1, ]), 3), "\n")
  cat("  beta2:", round(ac(params.all[[method]][2, ]), 3), "\n")
  cat("  sigma^2:", round(ac(sigma2.all[[method]]), 3), "\n")
}

cat("\nPosterior Summary Statistics:\n")
cat("=================================\n")
for(method in names(params.all)) {
  if(all(is.na(params.all[[method]]))) next
  
  cat("\n", method, ":\n")
  cat("beta1: mean =", round(mean(params.all[[method]][1, ], na.rm = TRUE), 3),
      ", sd =", round(sd(params.all[[method]][1, ], na.rm = TRUE), 3), "\n")
  cat("beta2: mean =", round(mean(params.all[[method]][2, ], na.rm = TRUE), 3),
      ", sd =", round(sd(params.all[[method]][2, ], na.rm = TRUE), 3), "\n")
  cat("sigma^2: mean =", round(mean(sigma2.all[[method]], na.rm = TRUE), 3),
      ", sd =", round(sd(sigma2.all[[method]], na.rm = TRUE), 3), "\n")
}

cat("\nTrue values: beta = (", theta.map[1], ",", theta.map[2], "), sigma^2 =", sigma2.map, "\n")

cat("\n==== ANALYSIS COMPLETE ====\n")

####### Conditional Posterior Densities #######

cat("\n==== CONDITIONAL POSTERIOR DENSITIES ====\n")

# Conditional posterior density for beta | sigma^2, y ~ N(mu_beta, sigma^2 * Sigma_beta)
conditional_beta_density <- function(beta, sigma2, X, y) {
  # Posterior mean: mu_beta = (X'X)^(-1) X'y
  XTX_inv <- solve(crossprod(X))
  XTy <- crossprod(X, y)
  mu_beta <- as.vector(XTX_inv %*% XTy)
  
  # Posterior covariance: Sigma_beta = sigma^2 * (X'X)^(-1)
  Sigma_beta <- sigma2 * XTX_inv
  
  # Evaluate multivariate normal density
  dmvnorm_manual <- function(x, mu, Sigma) {
    k <- length(x)
    diff <- x - mu
    exp(-0.5 * as.numeric(t(diff) %*% solve(Sigma) %*% diff)) / 
      sqrt((2*pi)^k * det(Sigma))
  }
  
  return(list(
    density = dmvnorm_manual(beta, mu_beta, Sigma_beta),
    mean = mu_beta,
    covariance = Sigma_beta
  ))
}

# Conditional posterior density for sigma^2 | beta, y ~ InverseGamma(a, b)
conditional_sigma2_density <- function(sigma2, beta, X, y) {
  # With Jeffreys prior p(sigma^2) ∝ 1/sigma^2:
  # Shape parameter: a = (n - p)/2
  # Scale parameter: b = SSR/2 where SSR = (y - X*beta)'(y - X*beta)
  
  n <- length(y)
  p <- ncol(X)
  
  # Sum of squared residuals
  residuals <- y - X %*% beta
  SSR <- sum(residuals^2)
  
  # Inverse Gamma parameters
  shape_a <- (n - p) / 2
  scale_b <- SSR / 2
  
  # Inverse Gamma density: f(x) = (b^a / Gamma(a)) * x^(-a-1) * exp(-b/x)
  log_density <- shape_a * log(scale_b) - lgamma(shape_a) - 
                 (shape_a + 1) * log(sigma2) - scale_b / sigma2
  density <- exp(log_density)
  
  return(list(
    density = density,
    log_density = log_density,
    shape = shape_a,
    scale = scale_b,
    mean = scale_b / (shape_a - 1),  # Mean of Inverse Gamma
    variance = scale_b^2 / ((shape_a - 1)^2 * (shape_a - 2))  # Variance of Inverse Gamma
  ))
}

# Demonstrate the conditional posteriors at MAP estimates
cat("\nConditional Posterior Evaluations at MAP Estimates:\n")
cat("===================================================\n")

# Evaluate beta | sigma^2 at MAP values
beta_posterior <- conditional_beta_density(theta.map, sigma2.map, X, y)
cat("p(beta | sigma^2, y) evaluated at MAP:\n")
cat("  Density:", format(beta_posterior$density, scientific = TRUE), "\n")
cat("  Posterior mean:", round(beta_posterior$mean, 4), "\n")
cat("  Posterior covariance:\n")
print(round(beta_posterior$covariance, 6))

# Evaluate sigma^2 | beta at MAP values  
sigma2_posterior <- conditional_sigma2_density(sigma2.map, theta.map, X, y)
cat("\np(sigma^2 | beta, y) evaluated at MAP:\n")
cat("  Density:", format(sigma2_posterior$density, scientific = TRUE), "\n")
cat("  Log-density:", round(sigma2_posterior$log_density, 4), "\n")
cat("  Shape parameter (a):", round(sigma2_posterior$shape, 4), "\n")
cat("  Scale parameter (b):", round(sigma2_posterior$scale, 4), "\n")
cat("  Posterior mean:", round(sigma2_posterior$mean, 4), "\n")
cat("  Posterior variance:", round(sigma2_posterior$variance, 6), "\n")

# Show the explicit mathematical forms
cat("\nExplicit Conditional Posterior Forms:\n")
cat("====================================\n")
cat("1. beta | sigma^2, y ~ N(mu_beta, sigma^2 * Sigma_beta)\n")
cat("   where:\n")
cat("   mu_beta = (X'X)^(-1) X'y =", round(beta_posterior$mean, 4), "\n")
cat("   Sigma_beta = (X'X)^(-1) = \n")
print(round(solve(crossprod(X)), 6))

cat("\n2. sigma^2 | beta, y ~ InverseGamma(a, b)\n")
cat("   where:\n")
cat("   a = (n - p)/2 =", sigma2_posterior$shape, "\n")
cat("   b = SSR/2 =", round(sigma2_posterior$scale, 4), "\n")
cat("   SSR = (y - X*beta)'(y - X*beta)\n")

# Verification: Sample from conditional posteriors and compare to MCMC samples
cat("\nVerification - Sampling from Conditional Posteriors:\n")
cat("===================================================\n")

set.seed(456)
# Sample beta | sigma^2 using the analytical form
n_verify <- 1000
beta_samples <- matrix(NA, n_verify, 2)
for(i in 1:n_verify) {
  sigma2_fixed <- sigma2.map  # Fix sigma^2 at MAP value
  beta_samples[i,] <- MASS::mvrnorm(1, beta_posterior$mean, sigma2_fixed * solve(crossprod(X)))
}

cat("Analytical beta | sigma^2 samples:\n")
cat("  Mean:", round(colMeans(beta_samples), 4), "\n")
cat("  SD:", round(apply(beta_samples, 2, sd), 4), "\n")

# Sample sigma^2 | beta using the analytical form
sigma2_samples <- rinvgamma(n_verify, sigma2_posterior$shape, sigma2_posterior$scale)

cat("Analytical sigma^2 | beta samples:\n")
cat("  Mean:", round(mean(sigma2_samples), 4), "\n")
cat("  SD:", round(sd(sigma2_samples), 4), "\n")

cat("\nComparison with MCMC samples (Joint Gibbs):\n")
gibbs_beta_mean <- rowMeans(params.all$Gibbs2[1:2, ], na.rm = TRUE)
gibbs_beta_sd <- apply(params.all$Gibbs2[1:2, ], 1, sd, na.rm = TRUE)
gibbs_sigma2_mean <- mean(sigma2.all$Gibbs2, na.rm = TRUE)
gibbs_sigma2_sd <- sd(sigma2.all$Gibbs2, na.rm = TRUE)

cat("MCMC beta samples:\n")
cat("  Mean:", round(gibbs_beta_mean, 4), "\n")
cat("  SD:", round(gibbs_beta_sd, 4), "\n")
cat("MCMC sigma^2 samples:\n")
cat("  Mean:", round(gibbs_sigma2_mean, 4), "\n")
cat("  SD:", round(gibbs_sigma2_sd, 4), "\n")

cat("\n==== CONDITIONAL POSTERIOR ANALYSIS COMPLETE ====\n") 


####### Ensuring σ² Positivity in MCMC #######

cat("\n==== METHODS FOR ENSURING σ² > 0 ====\n")

# Current approach: Log parameterization
# We parameterize as log(σ²) so σ² = exp(log(σ²)) > 0 always
cat("Current Implementation: Log Parameterization\n")
cat("  - Parameters: c(beta1, beta2, log(sigma^2))\n")  
cat("  - Ensures σ² = exp(log(σ²)) > 0 automatically\n")
cat("  - Jacobian adjustment handled in log-posterior\n\n")

# Alternative methods discussion
cat("Alternative Methods for Ensuring σ² > 0:\n")
cat("=========================================\n")

cat("1. TRUNCATION/REJECTION:\n")
cat("   - Simply reject any proposed σ² < 0\n")
cat("   - Pro: Simple to implement\n") 
cat("   - Con: Can lead to low acceptance rates near boundary\n\n")

cat("2. REFLECTION:\n")
cat("   - If σ² < 0 proposed, use |σ²| instead\n")
cat("   - Pro: No rejected proposals\n")
cat("   - Con: Can distort the posterior near zero\n\n")

cat("3. SQUARE ROOT PARAMETERIZATION:\n")
cat("   - Parameterize as σ instead of σ², then σ² = σ²\n")
cat("   - Pro: Natural positivity constraint\n")
cat("   - Con: Different Jacobian adjustment needed\n\n")

cat("4. LOG PARAMETERIZATION (Current):\n")
cat("   - Parameterize as η = log(σ²), so σ² = exp(η)\n")
cat("   - Pro: Automatic positivity, good numerical properties\n")
cat("   - Con: May concentrate mass away from zero\n\n")

cat("5. INVERSE PARAMETERIZATION:\n")
cat("   - Parameterize as precision τ = 1/σ², sample τ > 0\n")
cat("   - Pro: Conjugacy with normal likelihood\n")
cat("   - Con: Need to transform back to σ²\n\n")

# Demonstration function for truncation approach
MH_with_truncation <- function(U, proposal_sds, current_q) {
  proposed_q <- current_q + proposal_sds * rnorm(length(current_q))
  
  # Check if σ² = exp(proposed_q[3]) would be reasonable
  # (log parameterization handles this automatically, but for illustration)
  if(proposed_q[3] < -10) {  # Would give σ² < exp(-10) ≈ 0.000045
    return(list(q = current_q, accepted = FALSE))  # Reject extreme values
  }
  
  log_ratio <- U(current_q) - U(proposed_q)
  accept <- runif(1) < exp(log_ratio)
  if (accept) {
    return(list(q = proposed_q, accepted = TRUE))
  } else {
    return(list(q = current_q, accepted = FALSE))
  }
}

cat("Note: Our current log parameterization is generally preferred because:\n")
cat("  - Automatically ensures σ² > 0\n")
cat("  - Works well with gradient-based methods (HMC)\n") 
cat("  - Avoids boundary issues\n")
cat("  - Jacobian |dσ²/d(log σ²)| = σ² handled in log-posterior\n\n")

####### Advanced Tuning Parameter Analysis #######

cat("\n==== ADVANCED TUNING PARAMETER ANALYSIS ====\n")

# Sensitivity Analysis: How do acceptance rates vary with parameters?
cat("Sensitivity Analysis for Tuning Parameters\n")
cat("==========================================\n")

# 1. HMC Sensitivity Analysis
cat("\n1. HMC SENSITIVITY TO EPSILON AND L:\n")

# Create finer grids for sensitivity analysis  
eps_fine <- seq(0.01, 0.5, by = 0.02)
L_fine <- seq(2, 30, by = 2)
hmc_sensitivity <- array(NA, dim = c(length(eps_fine), length(L_fine), 3))
dimnames(hmc_sensitivity)[[3]] <- c("acceptance_rate", "effective_sample_size", "avg_energy_error")

cat("Running HMC sensitivity analysis (this may take a moment)...\n")

# Function to estimate effective sample size
eff_sample_size <- function(x) {
  x <- x[!is.na(x)]
  if(length(x) < 10) return(NA)
  
  # Calculate autocorrelation function
  autocor <- function(lag) {
    if(lag >= length(x)-1) return(0)
    cor(x[1:(length(x)-lag)], x[(lag+1):length(x)], use = "complete.obs")
  }
  
  # Find first negative autocorrelation or cutoff at lag 50
  max_lag <- min(50, length(x)-2)
  autocors <- sapply(1:max_lag, autocor)
  first_negative <- which(autocors < 0)[1]
  if(is.na(first_negative)) first_negative <- max_lag
  
  # Integrated autocorrelation time
  tau_int <- 1 + 2 * sum(autocors[1:first_negative], na.rm = TRUE)
  return(length(x) / (2 * tau_int))
}

# Sample subset for computational efficiency
eps_subset <- eps_fine[seq(1, length(eps_fine), by = 3)]
L_subset <- L_fine[seq(1, length(L_fine), by = 2)]

for(i in seq_along(eps_subset)) {
  for(j in seq_along(L_subset)) {
    eps <- eps_subset[i]
    L <- L_subset[j]
    
    # Run short HMC chain
    n_test <- 100
    accepts <- 0
    params_chain <- matrix(NA, n_test, 3)
    energy_errors <- numeric(n_test)
    
    params_curr <- c(0, 0, 0)
    for(k in 1:n_test) {
      set.seed(k + i*1000 + j*100)  # Ensure reproducibility
      
      # Calculate energy before
      current_U <- logpost(params_curr)
      current_K <- sum(rnorm(3)^2) / 2  # Simulate kinetic energy
      
      params_new <- HMC(logpost, gradpost, eps, L, params_curr)
      
      # Calculate energy after  
      proposed_U <- logpost(params_new)
      proposed_K <- sum(rnorm(3)^2) / 2
      
      if(!all(params_new == params_curr)) accepts <- accepts + 1
      params_curr <- params_new
      params_chain[k,] <- params_curr
      
      # Energy conservation error (should be small for good HMC)
      energy_errors[k] <- abs((current_U + current_K) - (proposed_U + proposed_K))
    }
    
    accept_rate <- accepts / n_test
    ess <- mean(apply(params_chain, 2, eff_sample_size), na.rm = TRUE)
    avg_energy_error <- mean(energy_errors, na.rm = TRUE)
    
    # Store results (map back to full grid indices)
    full_i <- which(eps_fine == eps)
    full_j <- which(L_fine == L)
    if(length(full_i) > 0 && length(full_j) > 0) {
      hmc_sensitivity[full_i, full_j, 1] <- accept_rate
      hmc_sensitivity[full_i, full_j, 2] <- ess
      hmc_sensitivity[full_i, full_j, 3] <- avg_energy_error
    }
  }
}

# 2. MH Sensitivity Analysis
cat("\n2. MH SENSITIVITY TO PROPOSAL VARIANCE:\n")

# Finer grid for MH analysis
mh_scales_fine <- seq(0.01, 1.0, by = 0.05)
mh_sensitivity <- matrix(NA, length(mh_scales_fine), 3)
colnames(mh_sensitivity) <- c("acceptance_rate", "effective_sample_size", "mixing_quality")

cat("Running MH sensitivity analysis...\n")

for(i in seq_along(mh_scales_fine)) {
  scale <- mh_scales_fine[i]
  proposal_sds <- c(scale, scale, scale * 1.5)
  
  # Run test chain
  n_test <- 200
  accepts <- 0
  params_chain <- matrix(NA, n_test, 3)
  
  params_curr <- c(0, 0, 0)
  for(k in 1:n_test) {
    set.seed(k + i*1000)
    result <- MH_improved(logpost, proposal_sds, params_curr)
    if(result$accepted) accepts <- accepts + 1
    params_curr <- result$q
    params_chain[k,] <- params_curr
  }
  
  accept_rate <- accepts / n_test
  ess <- mean(apply(params_chain, 2, eff_sample_size), na.rm = TRUE)
  
  # Mixing quality: variance of chain means across windows
  window_size <- 40
  n_windows <- floor(n_test / window_size)
  if(n_windows > 2) {
    window_means <- matrix(NA, n_windows, 3)
    for(w in 1:n_windows) {
      start_idx <- (w-1) * window_size + 1
      end_idx <- w * window_size
      window_means[w,] <- colMeans(params_chain[start_idx:end_idx,], na.rm = TRUE)
    }
    mixing_quality <- mean(apply(window_means, 2, var), na.rm = TRUE)
  } else {
    mixing_quality <- NA
  }
  
  mh_sensitivity[i,] <- c(accept_rate, ess, mixing_quality)
}

# Plotting and Analysis
cat("\n3. TUNING PARAMETER RECOMMENDATIONS:\n")
cat("====================================\n")

# Find optimal parameters based on effective sample size
if(any(!is.na(hmc_sensitivity[,,2]))) {
  best_hmc_idx <- which(hmc_sensitivity[,,2] == max(hmc_sensitivity[,,2], na.rm = TRUE), arr.ind = TRUE)[1,]
  best_eps <- eps_fine[best_hmc_idx[1]]
  best_L <- L_fine[best_hmc_idx[2]]
  
  cat("HMC Recommendations:\n")
  cat("  Best epsilon (by ESS):", best_eps, "\n")
  cat("  Best L (by ESS):", best_L, "\n")
  cat("  Corresponding acceptance rate:", 
      round(hmc_sensitivity[best_hmc_idx[1], best_hmc_idx[2], 1], 3), "\n")
  cat("  Effective sample size:", 
      round(hmc_sensitivity[best_hmc_idx[1], best_hmc_idx[2], 2], 1), "\n")
}

if(any(!is.na(mh_sensitivity[,2]))) {
  best_mh_idx <- which.max(mh_sensitivity[,2])
  best_mh_scale <- mh_scales_fine[best_mh_idx]
  
  cat("\nMH Recommendations:\n")
  cat("  Best proposal scale (by ESS):", best_mh_scale, "\n")
  cat("  Corresponding acceptance rate:", 
      round(mh_sensitivity[best_mh_idx, 1], 3), "\n")
  cat("  Effective sample size:", 
      round(mh_sensitivity[best_mh_idx, 2], 1), "\n")
}

# 4. Discussion of Existing Tuning Algorithms
cat("\n4. EXISTING ADAPTIVE TUNING ALGORITHMS:\n")
cat("======================================\n")

cat("A. METROPOLIS-HASTINGS TUNING:\n")
cat("   i) Roberts & Rosenthal Optimal Scaling:\n")
cat("      - Target acceptance rate: 23.4% (high dimensions)\n")
cat("      - Proposal variance ∝ d^(-1) where d = dimension\n")
cat("      - Rule of thumb: accept rate 20-50% is reasonable\n\n")

cat("   ii) Adaptive Metropolis (Haario et al.):\n")
cat("       - Update proposal covariance based on sample covariance\n")
cat("       - C_n = s_d * Cov(X_1, ..., X_{n-1}) + s_d * ε * I_d\n")
cat("       - s_d = (2.38)^2/d for optimal scaling\n\n")

cat("   iii) Diminishing Adaptation:\n")
cat("        - Acceptance rate: r_n = r_{n-1} + γ_n(α_n - α*)\n")
cat("        - Step size: σ_n = σ_{n-1} * exp(γ_n(α_n - α*))\n")
cat("        - γ_n = n^(-c) with c ∈ (0.5, 1]\n\n")

cat("B. HMC TUNING:\n")
cat("   i) Dual Averaging (Hoffman & Gelman):\n")
cat("      - Adapts step size ε to target acceptance rate\n") 
cat("      - Uses stochastic approximation with diminishing adaptation\n")
cat("      - ε_n = exp(μ - sqrt(n)/γ * H_n)\n")
cat("      - H_n tracks deviation from target acceptance\n\n")

cat("   ii) No-U-Turn Sampler (NUTS):\n")
cat("       - Automatically chooses number of leapfrog steps L\n")
cat("       - Continues until trajectory starts to reverse\n")
cat("       - Eliminates need to tune L parameter\n\n")

cat("   iii) Target Acceptance Rates:\n")
cat("        - Optimal acceptance rate: ~65% (Neal, 2011)\n")
cat("        - Higher than MH due to informed proposals\n")
cat("        - Energy conservation provides additional info\n\n")

# 5. Practical Implementation Example
cat("5. ADAPTIVE MH IMPLEMENTATION EXAMPLE:\n")
cat("=====================================\n")

# Simple adaptive MH algorithm
adaptive_MH <- function(n_iter = 1000, target_accept = 0.25, adapt_freq = 50) {
  params <- matrix(NA, n_iter, 3)
  proposal_sd <- c(0.1, 0.1, 0.15)  # Initial proposal
  accepts <- 0
  
  params_curr <- c(0, 0, 0)
  
  for(i in 1:n_iter) {
    # Propose new state
    proposed <- params_curr + proposal_sd * rnorm(3)
    
    # Accept/reject
    log_ratio <- logpost(params_curr) - logpost(proposed)
    if(runif(1) < exp(log_ratio)) {
      params_curr <- proposed
      accepts <- accepts + 1
    }
    
    params[i,] <- params_curr
    
    # Adapt proposal every 'adapt_freq' iterations
    if(i %% adapt_freq == 0 && i > adapt_freq) {
      recent_accept_rate <- accepts / adapt_freq
      
      # Simple adaptation rule
      if(recent_accept_rate > target_accept + 0.05) {
        proposal_sd <- proposal_sd * 1.1  # Increase step size
      } else if(recent_accept_rate < target_accept - 0.05) {
        proposal_sd <- proposal_sd * 0.9  # Decrease step size
      }
      
      # Reset counter
      accepts <- 0
      
      if(i <= 500) {  # Print adaptation progress
        cat("Iteration", i, ": Accept rate =", round(recent_accept_rate, 3), 
            ", New proposal SD =", round(proposal_sd[1], 4), "\n")
      }
    }
  }
  
  return(list(samples = params, final_proposal_sd = proposal_sd))
}

cat("Running adaptive MH example (first 500 iterations shown):\n")
adaptive_result <- adaptive_MH(n_iter = 1000)

cat("\nFinal adapted proposal standard deviations:\n")
cat("  beta1, beta2:", round(adaptive_result$final_proposal_sd[1:2], 4), "\n")
cat("  log(sigma^2):", round(adaptive_result$final_proposal_sd[3], 4), "\n")

# Compare with our grid search results
cat("\nComparison with Grid Search Results:\n")
cat("  Grid search optimal scale:", optimal_mh_scale, "\n")
cat("  Adaptive MH final scale:", round(adaptive_result$final_proposal_sd[1], 4), "\n")

cat("\n6. KEY INSIGHTS ON PARAMETER SENSITIVITY:\n")
cat("========================================\n")
cat("• MH is quite robust to proposal variance within reasonable range\n")
cat("• Too small → slow mixing, high autocorrelation\n") 
cat("• Too large → low acceptance, poor exploration\n")
cat("• HMC more sensitive to step size ε than number of steps L\n")
cat("• Small ε → high acceptance but slow exploration\n")
cat("• Large ε → poor energy conservation, low acceptance\n")
cat("• L mainly affects computational cost vs mixing\n")
cat("• Adaptive methods generally outperform fixed tuning\n")

cat("\n==== TUNING PARAMETER ANALYSIS COMPLETE ====\n")

