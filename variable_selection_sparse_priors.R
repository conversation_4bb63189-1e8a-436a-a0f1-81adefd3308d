rm(list = ls())

# Download & load the data
url <- "https://raw.githubusercontent.com/mannheimSDS/mannheimSDS.github.io/main/data/conjointBS.Rdata"
tmp <- tempfile(); download.file(url, tmp, mode = "wb"); load(tmp)

library(pacman)
p_load(tictoc, glmnet, brms, dplyr, lme4)

# Build the design matrix
X.lm <- cbind(alldata$x_only,
              alldata$treat_main,
              alldata$treat_interactions)

y.lm <- alldata$y
colnames(X.lm) <- make.names(colnames(X.lm))
colnames(X.lm) <- gsub("__","_", colnames(X.lm))
colnames(X.lm) <- gsub("_",".",  colnames(X.lm))

# Explore dimensionality
n_main_effects <- ncol(alldata$x_only)
n_interactions <- ncol(alldata$treat_interactions)
n_obs <- nrow(alldata$x_only)
cat("Number of main effects:", n_main_effects, "\n")
cat("Number of interaction terms:", n_interactions, "\n")
cat("Number of observations:", n_obs, "\n")

## How many columns?
lapply(alldata, dim)
dim(X.lm)

# Baseline OLS
lm1 <- lm(y.lm ~ X.lm)
lmer1 <- lm(y.lm ~ X.lm + (1|id), data   = data.frame(y.lm, X.lm, "id"=alldata$id))

summary(lm1)
summary(lmer1)

# Count non-zero coefficients
lm1_coef <- coef(lm1)
lmer1_coef <- coef(lmer1)

# For OLS model
lm1_nonzero <- sum(abs(lm1_coef) > 1e-10)  # Using small threshold for numerical precision
cat("OLS non-zero coefficients:", lm1_nonzero, "out of", length(lm1_coef), "\n")

# For mixed effects model
lmer1_nonzero <- sum(abs(lmer1_coef) > 1e-10)
cat("Mixed effects non-zero coefficients:", lmer1_nonzero, "out of", length(lmer1_coef), "\n")


# -----------------------------------------------
# Cross-validated LASSO
# -----------------------------------------------
set.seed(1)
g1 <- cv.glmnet(x = X.lm, y = y.lm, family = "gaussian")
glm_coef <- as.vector(coef(g1, s = "lambda.1se"))
names(glm_coef) <- rownames(coef(g1))
glm_coef[glm_coef != 0]
length(glm_coef[glm_coef != 0])
cat("Non-zero LASSO betas:", sum(glm_coef != 0), "\n")

# ---------------------------------
# 4.  Variational Horseshoe
# ---------------------------------
priors <- c(
  prior(normal(0, 10), class = "Intercept"),
  prior(horseshoe(df = 4), class = "b")
)

tic("Mean‑field HS")
fit_vhs <- brm(
  y.lm ~ .,
  data      = data.frame(y.lm, X.lm),
  family    = gaussian(),
  prior     = priors,
  algorithm = "meanfield",
  silent    = TRUE
)
toc()

fixef_vhs <- fixef(fit_vhs)
active_vhs <- fixef_vhs[abs(fixef_vhs[, "Estimate"]) > 0.01, ]
print(active_vhs[1:10, ], digits = 3)




