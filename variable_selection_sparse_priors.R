rm(list = ls())

# Download & load the data
url <- "https://raw.githubusercontent.com/mannheimSDS/mannheimSDS.github.io/main/data/conjointBS.Rdata"
tmp <- tempfile(); download.file(url, tmp, mode = "wb"); load(tmp)

library(pacman)
p_load(tictoc, glmnet, brms, dplyr, lme4)

# Build the design matrix
X.lm <- cbind(alldata$x_only,
              alldata$treat_main,
              alldata$treat_interactions)

y.lm <- alldata$y
colnames(X.lm) <- make.names(colnames(X.lm))
colnames(X.lm) <- gsub("__","_", colnames(X.lm))
colnames(X.lm) <- gsub("_",".",  colnames(X.lm))

# Explore dimensionality
n_main_effects <- ncol(alldata$x_only)
n_interactions <- ncol(alldata$treat_interactions)
n_obs <- nrow(alldata$x_only)
cat("Number of main effects:", n_main_effects, "\n")
cat("Number of interaction terms:", n_interactions, "\n")
cat("Number of observations:", n_obs, "\n")

## How many columns?
lapply(alldata, dim)
dim(X.lm)

# Baseline OLS
lm1 <- lm(y.lm ~ X.lm)
lmer1 <- lm(y.lm ~ X.lm + (1|id), data   = data.frame(y.lm, X.lm, "id"=alldata$id))

summary(lm1)
summary(lmer1)







