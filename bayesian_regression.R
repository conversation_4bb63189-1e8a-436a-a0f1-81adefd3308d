# SETWD ===========================
rm(list=ls())


# LIBRARIES ===========================
library(pacman)
p_load(tidyverse, foreign, stargazer, lmtest, multiwayvcov, brms)

# IMPORT DATA ===========================
dfgi <- read.csv("https://raw.githubusercontent.com/mannheimSDS/mannheimSDS.github.io/refs/heads/main/data/erosion.csv")

# MAIN ANALYSIS ===========================
temp <- dfgi%>%
  filter(!is.na(gini_disp))

## Descriptive Stats ----
# number of countries:
length(unique(temp$country.name))

# number of observations
length((temp$country.name))

# percent of observations eroding:
mean(temp$erosion.strict)

## TAB 1. Logit Regression Explaining Erosion ----
m1 <- glm(erosion.strict ~ gini_disp, data=temp, family=binomial)
crse1 <- coeftest(m1, cluster.vcov(m1, temp$country.name))
crse1
nobs(m1)

m2 <- glm(erosion.strict ~ gini_disp + log(gdppc), data=temp, family=binomial)
crse2 <- coeftest(m2, cluster.vcov(m2, temp$country.name))
crse2
nobs(m2)

m3 <- glm(erosion.strict ~ gini_disp + log(gdppc) + (year), 
          data=temp, family=binomial)
crse3 <- coeftest(m3, cluster.vcov(m3, temp$country.name))
crse3
nobs(m3)

stargazer(crse1,crse2,crse3, title="Logit",digits=3,type="text",
          header=F,df=F,omit.stat=c("f","ser","aic","ll"),
          add.lines = list(c("Observations", nobs(crse1), nobs(crse2), nobs(crse3))),
          star.cutoffs=c(0.1,0.05,0.01,0.001),
          omit = "factor\\(year\\)",
          star.char=c("\\dagger","*","**","***"),
          notes="$^{\\dagger} p<0.1$; $^{*} p<0.05$; $^{**} p<0.01$; $^{***} p<0.001$",
          covariate.labels=c("Gini","Logged GDP per capita",
                             "Year"),
          notes.append=F,table.placement="h!")

# BAYESIAN REGRESSION ===========================
library(brms)

set.seed(1)

# Original models without random effects
b1_mf <- brm(erosion.strict ~ gini_disp + log(gdppc) + (year), 
           data=temp, family = bernoulli(link = "logit"),
          algorithm = "meanfield"
)

b1_fr <- brm(erosion.strict ~ gini_disp + log(gdppc) + (year), 
           data=temp, family = bernoulli(link = "logit"),
          algorithm = "fullrank"
)

# Models with random intercepts by country
b2_mf <- brm(erosion.strict ~ gini_disp + log(gdppc) + (year) + (1 | country.name), 
           data=temp, family = bernoulli(link = "logit"),
          algorithm = "meanfield"
)

b2_fr <- brm(erosion.strict ~ gini_disp + log(gdppc) + (year) + (1 | country.name), 
           data=temp, family = bernoulli(link = "logit"),
          algorithm = "fullrank"
)

# Models with random intercepts and slopes for gini_disp by country
b3_mf <- brm(erosion.strict ~ gini_disp + log(gdppc) + (year) + (1 + gini_disp | country.name), 
           data=temp, family = bernoulli(link = "logit"),
          algorithm = "meanfield"
)
 
b3_fr <- brm(erosion.strict ~ gini_disp + log(gdppc) + (year) + (1 + gini_disp | country.name), 
           data=temp, family = bernoulli(link = "logit"),
          algorithm = "fullrank"
)

print(b1_mf, digits = 3)
print(b1_fr, digits = 3)

print("Models with random intercepts by country:")
print(b2_mf, digits = 3)
print(b2_fr, digits = 3)

print("Models with random intercepts and slopes for gini_disp by country:")
print(b3_mf, digits = 3)
print(b3_fr, digits = 3)

# Models with smoothing splines
print("Models with smoothing splines:")

# Model with smooth term for year (temporal trend)
b4_mf <- brm(erosion.strict ~ s(gini_disp) + s(log(gdppc)) + s(year) + (1 | country.name), 
           data=temp, family = bernoulli(link = "logit"),
          algorithm = "meanfield"
)

b4_fr <- brm(erosion.strict ~ s(gini_disp) + s(log(gdppc)) + s(year) + (1 | country.name), 
           data=temp, family = bernoulli(link = "logit"),
          algorithm = "fullrank"
)

# Model with smooth terms and random effects (more complex)
b5_mf <- brm(erosion.strict ~ s(gini_disp) + s(log(gdppc)) + s(year) + (1 + gini_disp | country.name), 
           data=temp, family = bernoulli(link = "logit"),
          algorithm = "meanfield"
)

b5_fr <- brm(erosion.strict ~ s(gini_disp) + s(log(gdppc)) + s(year) + (1 + gini_disp | country.name), 
           data=temp, family = bernoulli(link = "logit"),
          algorithm = "fullrank"
)

print("Model with smooth terms (random intercepts):")
print(b4_mf, digits = 3)
print(b4_fr, digits = 3)

print("Model with smooth terms and random slopes:")
print(b5_mf, digits = 3)
print(b5_fr, digits = 3)

# Plot smooth terms
print("Plotting smooth terms from meanfield model:")
plot(b4_mf, ask = FALSE)
plot(b5_mf, ask = FALSE)




# IMPROVED BAYESIAN MODEL ==========================
# First, let's explore the data to understand missing patterns
print(paste("Total observations:", nrow(temp)))
print("Missing data patterns:")
missing_summary <- temp %>%
  summarise(
    democracy_duration = sum(is.na(democracy_duration)),
    elec_any = sum(is.na(elec.any)),
    inc_top10 = sum(is.na(inc.top10)),
    inc_top1 = sum(is.na(inc.top1)),
    v2cacamps = sum(is.na(v2cacamps)),
    region = sum(is.na(region))
  )
print(missing_summary)

# Create analysis dataset with complete cases for key variables
temp_complete <- temp %>%
  filter(!is.na(democracy_duration) & 
         !is.na(elec.any) & 
         !is.na(inc.top10) &
         !is.na(region)) %>%
  mutate(
    # Create meaningful transformations
    log_democracy_duration = log(democracy_duration + 1), # +1 to handle potential zeros
    gini_centered = scale(gini_disp)[,1], # Center and scale
    gdppc_log_centered = scale(log(gdppc))[,1],
    inc_top10_centered = scale(inc.top10)[,1],
    # Create election year indicator
    election_year = as.numeric(elec.any == 1),
    # Create time trend
    year_centered = scale(year)[,1]
  )

print(paste("Complete cases for improved model:", nrow(temp_complete)))
print(paste("Countries in improved model:", length(unique(temp_complete$country.name))))
print(paste("Regions in improved model:", length(unique(temp_complete$region))))

# Set informative priors
priors <- c(
  # Priors for fixed effects (regularizing)
  prior(normal(0, 1), class = Intercept),
  prior(normal(0, 0.5), class = b, coef = gini_centered),
  prior(normal(0, 0.5), class = b, coef = gdppc_log_centered),
  prior(normal(0, 0.5), class = b, coef = inc_top10_centered),
  prior(normal(0, 0.3), class = b, coef = log_democracy_duration),
  prior(normal(0, 0.3), class = b, coef = election_year),
  prior(normal(0, 0.2), class = b, coef = year_centered),
  # Priors for random effects (conservative)
  prior(normal(0, 0.5), class = sd, group = country.name),
  prior(normal(0, 0.3), class = sd, group = region)
)

# My Model: Hierarchical structure with region and country effects
print("Fitting improved hierarchical model...")
improved_model <- brm(
  erosion.strict ~ 
    gini_centered +                    # Centered Gini coefficient
    gdppc_log_centered +              # Centered log GDP per capita  
    inc_top10_centered +              # Income concentration (top 10%)
    log_democracy_duration +          # Log of democratic experience
    election_year +                   # Electoral vulnerability
    year_centered +                   # Temporal trend
    (1 | region) +                   # Regional random intercepts
    (1 + gini_centered | country.name), # Country random intercepts + slopes for inequality
  data = temp_complete,
  family = bernoulli(link = "logit"),
  prior = priors,
  algorithm = "fullrank", # Use full-rank for better approximation
  iter = 4000,           # More iterations for better convergence
  warmup = 2000,
  chains = 4,
  cores = 4,
  control = list(adapt_delta = 0.95) # Higher adapt_delta for better sampling
)

# LONGITUDINAL LAGGED MODEL ===========================

# Autoregressive structure
# Create lagged erosion variable within countries
temp_ar <- temp_complete %>%
  arrange(country.name, year) %>%
  group_by(country.name) %>%
  mutate(
    erosion_lag1 = lag(erosion.strict, 1),
    erosion_lag2 = lag(erosion.strict, 2)
  ) %>%
  filter(!is.na(erosion_lag1)) %>%  # Remove first year per country
  ungroup()

print(paste("Observations with lagged variables:", nrow(temp_ar)))

model_autoregressive <- brm(
  erosion.strict ~ 
    erosion_lag1 +                    # Previous year erosion 
    erosion_lag2 +                    # Two years ago 
    gini_centered +
    gdppc_log_centered +
    inc_top10_centered +
    log_democracy_duration +
    election_year +
    s(year, k = 8) +                  # Smooth time trend
    (1 | region) +
    (1 + gini_centered | country.name),
  data = temp_ar,
  family = bernoulli(link = "logit"),
  prior = c(
    prior(normal(0, 1), class = Intercept), # Intercept
    prior(normal(2, 1), class = b, coef = erosion_lag1),    # Strong positive prior for persistence
    prior(normal(0.5, 0.5), class = b, coef = erosion_lag2), # Weaker effect for lag 2
    prior(normal(0, 0.5), class = b), # Fixed effects (Gini, GDP, etc.)
    prior(normal(0, 0.5), class = sd, group = country.name), # Country random effects
    prior(normal(0, 0.3), class = sd, group = region), # Regional random effects
    prior(inv_gamma(5, 5), class = sds) # Smoothing parameter for time trend  
  ),
  algorithm = "fullrank",
  iter = 4000,
  warmup = 2000,
  chains = 4,
  cores = 4,
  control = list(adapt_delta = 0.95) 
)

summary(model_autoregressive)



# FREQUENTIST VERSION OF THE SAME MODEL ===========================

# Load required packages for mixed effects
p_load(lme4, splines, car)


# Polynomial time trend
temp_ar$year_poly2 <- poly(temp_ar$year, 2)[,1]
temp_ar$year_poly3 <- poly(temp_ar$year, 2)[,2]

# Model with polynomial time trend
freq_model_poly <- glmer(
  erosion.strict ~ 
    erosion_lag1 +                    # Previous year erosion 
    erosion_lag2 +                    # Two years ago 
    gini_centered +
    gdppc_log_centered +
    inc_top10_centered +
    log_democracy_duration +
    election_year +
    year_poly2 + year_poly3 +         # Polynomial time trend
    (1 | region) +                    # Regional random intercepts
    (1 + gini_centered | country.name), # Country RI + Slopes
  data = temp_ar,
  family = binomial(link = "logit"),
  control = glmerControl(optimizer = "bobyqa", optCtrl = list(maxfun = 100000))
)

summary(freq_model_poly)
