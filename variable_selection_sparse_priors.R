rm(list = ls())

# Download & load the data
url <- "https://raw.githubusercontent.com/mannheimSDS/mannheimSDS.github.io/main/data/conjointBS.Rdata"
tmp <- tempfile(); download.file(url, tmp, mode = "wb"); load(tmp)

library(pacman)
p_load(tictoc, glmnet, brms, dplyr, lme4)

# Build the design matrix
X.lm <- cbind(alldata$x_only,
              alldata$treat_main,
              alldata$treat_interactions)

y.lm <- alldata$y
colnames(X.lm) <- make.names(colnames(X.lm))
colnames(X.lm) <- gsub("__","_", colnames(X.lm))
colnames(X.lm) <- gsub("_",".",  colnames(X.lm))

# Explore dimensionality
n_main_effects <- ncol(alldata$x_only)
n_interactions <- ncol(alldata$treat_interactions)
n_obs <- nrow(alldata$x_only)
cat("Number of main effects:", n_main_effects, "\n")
cat("Number of interaction terms:", n_interactions, "\n")
cat("Number of observations:", n_obs, "\n")

## How many columns?
lapply(alldata, dim)
dim(X.lm)

# Baseline OLS
lm1 <- lm(y.lm ~ X.lm)
lmer1 <- lm(y.lm ~ X.lm + (1|id), data   = data.frame(y.lm, X.lm, "id"=alldata$id))

# Count non-zero coefficients
lm1_coef <- coef(lm1)
lmer1_coef <- coef(lmer1)

# Check for NA values
cat("OLS model - NA coefficients:", sum(is.na(lm1_coef)), "\n")
cat("Mixed effects model - NA coefficients:", sum(is.na(lmer1_coef)), "\n")

# For OLS model (excluding NAs)
lm1_nonzero <- sum(abs(lm1_coef) > 1e-10, na.rm = TRUE)
lm1_valid <- sum(!is.na(lm1_coef))
cat("OLS non-zero coefficients:", lm1_nonzero, "out of", lm1_valid, "valid coefficients\n")

# For mixed effects model (excluding NAs)
lmer1_nonzero <- sum(abs(lmer1_coef) > 1e-10, na.rm = TRUE)
lmer1_valid <- sum(!is.na(lmer1_coef))
cat("Mixed effects non-zero coefficients:", lmer1_nonzero, "out of", lmer1_valid, "valid coefficients\n")

# Covariate-by-covariate regression with p-value selection
univariate_results <- data.frame(
  covariate = paste0("X", 1:ncol(X)),
  coefficient = NA,
  p_value = NA,
  significant_05 = FALSE,
  significant_01 = FALSE
)

for(i in 1:ncol(X)) {
  fit <- lm(y ~ X[,i])
  coef_summary <- summary(fit)$coefficients[2,]
  
  univariate_results$coefficient[i] <- coef_summary[1]
  univariate_results$p_value[i] <- coef_summary[4]
  univariate_results$significant_05[i] <- coef_summary[4] < 0.05
  univariate_results$significant_01[i] <- coef_summary[4] < 0.01
}

# Count significant variables
cat("Univariate regression results:\n")
cat("Variables significant at p < 0.05:", sum(univariate_results$significant_05), "\n")
cat("Variables significant at p < 0.01:", sum(univariate_results$significant_01), "\n")

# Show most significant
top_vars <- head(univariate_results[order(univariate_results$p_value),], 10)
print(top_vars)

# Compare to true sparse structure (first 5 variables have non-zero coefficients)
true_predictors <- 1:5
detected_05 <- which(univariate_results$significant_05)
detected_01 <- which(univariate_results$significant_01)

cat("\nTrue positive rate (p < 0.05):", mean(true_predictors %in% detected_05), "\n")
cat("False positive rate (p < 0.05):", mean(!(detected_05 %in% true_predictors)), "\n")


# -----------------------------------------------
# Cross-validated LASSO
# -----------------------------------------------
set.seed(1)
g1 <- cv.glmnet(x = X.lm, y = y.lm, family = "gaussian")

glm_coef <- as.vector(coef(g1, s = "lambda.1se"))
names(glm_coef) <- rownames(coef(g1))
glm_coef[glm_coef != 0]
length(glm_coef[glm_coef != 0])
cat("Non-zero LASSO betas:", sum(glm_coef != 0), "\n")

# ---------------------------------
# 4.  Variational Horseshoe
# ---------------------------------
priors <- c(
  prior(normal(0, 10), class = "Intercept"),
  prior(horseshoe(df = 4), class = "b")
)

tic("Mean‑field HS")
fit_vhs <- brm(
  y.lm ~ .,
  data      = data.frame(y.lm, X.lm),
  family    = gaussian(),
  prior     = priors,
  algorithm = "meanfield",
  silent    = TRUE
)
toc()

fixef_vhs <- fixef(fit_vhs)
active_vhs <- fixef_vhs[abs(fixef_vhs[, "Estimate"]) > 0.01, ]
print(active_vhs[1:10, ], digits = 3)


# Count non-zero coefficients for Variational Horseshoe
vhs_total_coef <- nrow(fixef_vhs)
vhs_nonzero_01 <- sum(abs(fixef_vhs[, "Estimate"]) > 0.01)
vhs_nonzero_001 <- sum(abs(fixef_vhs[, "Estimate"]) > 0.001)
vhs_nonzero_small <- sum(abs(fixef_vhs[, "Estimate"]) > 1e-10)

cat("Variational Horseshoe coefficients:\n")
cat("  Total coefficients:", vhs_total_coef, "\n")
cat("  |coef| > 0.01:", vhs_nonzero_01, "\n")
cat("  |coef| > 0.001:", vhs_nonzero_001, "\n")
cat("  |coef| > 1e-10:", vhs_nonzero_small, "\n")



